import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MESSAGES, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { environment } from '@env/environment';
import { VendorListItem } from '@pages/administration/models';
import { ContactDetails } from '@pages/administration/models/crm.model';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { BannerListItem } from '@pages/administration/pages/public-page-config/pages/advertising/advertising.model';
import { AdvertisingService } from '@pages/administration/pages/public-page-config/pages/advertising/advertising.service';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { CrmService } from '@pages/crm/services/crm.service';
import { GeneralInformationFormGroup, InternetGroups, InternetOptionFormGroup, InventoryGeneralDetails, InventoryListItem, LotLocationFormGroup, ModelType, OdometerReadingFormGroup, PipelineDetails, PreviousOwnerFormGroup, UnitAssociation, WarrantyCreateParams } from '@pages/inventory/models';
import { GeneralInfoService } from '@pages/inventory/services/general-info.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { PipelineDetailsService } from '@pages/inventory/services/pipeline-details.service';
import { SoldTruckBoardListItem } from '@pages/pipeline/models';
import { IncomingTruckBoardListItem } from '@pages/transport/models/incoming-truck.model';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { IdNameModel } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';
@Component({
  selector: 'app-inventory-general-info',
  templateUrl: './inventory-general-info.component.html',
  styleUrls: ['./inventory-general-info.component.scss']
})
export class InventoryGeneralInfoComponent extends BaseComponent implements OnInit, OnChanges {
  ModelType = ModelType;
  generalInfoFormGroup!: FormGroup;
  odometerReadingFormGroup!: FormGroup;
  unitOfDistanceFormGroup!: FormGroup;
  previousOwnerFormGroup!: FormGroup;
  internetOptionFormGroup!: FormGroup;
  lotLocationFormGroup!: FormGroup;
  internetGroupForm!: FormGroup;
  unitAssociationForm!: FormGroup;
  warrantiesInputValues: WarrantyCreateParams[] = [];
  makes: IdNameModel[] = [];
  models: IdNameModel[] = [];
  categories: IdNameModel[] = [];
  years: IdNameModel[] = [];
  unitTypes: IdNameModel[] = [];
  categoryTypes: IdNameModel[] = [];
  designations: IdNameModel[] = [];
  vendors: IdNameModel[] = [];
  unitsOfDistance: IdNameModel[] = [];
  warrantyRadioOptions: Array<{ label: string, value: string }> = [];
  warranties: IdNameModel[] = [];
  internetGroupOptions: IdNameModel[] = [];
  dealerOptions: IdNameModel[] = [];
  inventoryStatuses: IdNameModel[] = [];
  internetGroup: number[] = [];
  internetGroupCopy: InternetGroups[] = [];
  internetGroupInputValues: InternetGroups[] = [];
  contactList: ContactDetails[] = [];
  selectedVendor!: VendorListItem | null;
  unitOfDistance = 1;
  displayContact!: any;
  inventoryDetails!: InventoryGeneralDetails;
  pipelineDetails!: PipelineDetails;
  unitId!: number | undefined;
  selectedTruckBoard!: SoldTruckBoardListItem | null;
  selectedInventoryInfo!: InventoryListItem;
  soldInventoriesId!: Array<number>;
  holdInventoriesId!: Array<number>;
  isEditMode = false;
  modelPopups = {
    showCreateVendor: false,
    showCreateSupplier: false,
    showCreateContact: false,
    showCreateModel: false,
    showCreateMake: false,
    showSoldPipelineDetails: false,
    showStockPipelineDetails: false,
    showCreateUnitType: false,
    showInventoryToBeSoldModal: false,
    showInventoryToBeHoldModal: false,
    showAdvertisingBanner: false,
  };
  environment = environment;
  banners: BannerListItem[] = [];
  currentUser!: Account;
  accordionTabs = {
    generalInfo: true,
    previousOwner: true,
    odometerReadings: true,
    lotLocation: true,
    internetOptions: true,
    pipelineProgress: true,
  };

  loaders = {
    makes: false,
    models: false,
    categories: false,
    years: false,
    status: false,
    unitTypes: false,
    designations: false,
    vendors: false,
    previousOwnerName: false,
    lotLocation: false,
    currentLocation: false,
    internetGroup: false,
    ownedBy: false,
    categoryType: false,
    advertising: false
  };
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  displaySelectionDialog!: boolean;
  categoryId!: number | null;
  selectedMakeId!: number | null;
  disableStatus = false;
  currentInventoryStatus: number | null;
  @Input() isViewMode!: boolean;
  @Input() inventoryIncomingInfo!: IncomingTruckBoardListItem | null;
  @Input() inventoryInfo!: InventoryListItem | null;
  @Input() categoriesToShow!: Array<IdNameModel>;
  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onChangeActiveIndex: EventEmitter<boolean> = new EventEmitter<boolean>()
  @Output() showSoldTab: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() showHoldTab: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onCategoryChange = new EventEmitter<number | null>();
  @Output() showLoader = new EventEmitter<boolean>();

  constructor(private readonly formBuilder: FormBuilder,
    private readonly inventoryService: InventoryService,
    private readonly commonService: CommonService,
    private readonly dealerService: DealerService,
    private readonly generalInfoService: GeneralInfoService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly crmService: CrmService,
    private readonly pipelineDetailsService: PipelineDetailsService,
    private readonly commonSharedService: CommonSharedService,
    private readonly advertisingService: AdvertisingService,
    private readonly authService: AuthService,
    private readonly confirmationService: ConfirmationService,
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.commonSharedService.setMessage$('')
    this.commonSharedService.setBlockUI$(true);
    this.getCurrentUser();
    this.initializeFormGroup();
    this.getPipelineDetails();
    await this.getInventoryById();
    this.getAll();
    this.commonSharedService.setBlockUI$(false);
    this.inventoryService.contactGeneralLoaded.asObservable().subscribe({
      next: (response: boolean) => {
        if (response) {
          this.getVendorList();
        }
      }
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.inventoryInfo?.currentValue || changes.inventoryIncomingInfo?.currentValue) {
      this.isEditMode = true;
    }
    if (changes.categoriesToShow?.currentValue) {
      this.categoryTypes = this.categoriesToShow;
    }
    if (!this.isViewMode) {
      this.generalInfoFormGroup?.enable();
      this.odometerReadingFormGroup?.enable();
      this.unitOfDistanceFormGroup?.enable();
      this.internetOptionFormGroup?.enable();
      this.lotLocationFormGroup?.enable();
      this.previousOwnerFormGroup?.enable();
      this.internetGroupForm?.enable();
    }
  }

  getCurrentUser() {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe((user: Account | null) => {
      this.currentUser = user as Account;
    });
  }

  private getAll() {
    this.getInventoryStatuses();
    this.getDesignationList();
    this.getVendorList();
    this.getInternetGroupList();
    this.getDealersList();
    this.getContactInfo();
    this.getCategoryTypes();
    this.getBanners();
  }

  private initializeFormGroup(): void {
    const activeDealerLocation = this.currentUser?.currentlyActiveDealer?.id ? this.currentUser?.currentlyActiveDealer?.id : null

    this.generalInfoFormGroup = this.formBuilder.group({
      generalInformation: this.formBuilder.group({
        unitTypeId: new FormControl(null, [Validators.required]),
        year: new FormControl(new Date().getFullYear(), [Validators.required]),
        vin: new FormControl(null),
        stockNumber: new FormControl(null, [Validators.required]),
        designationId: new FormControl(null, [Validators.required]),
        unitStatusId: new FormControl(null, [Validators.required]),
        makeId: new FormControl(null, [Validators.required]),
        unitModelId: new FormControl(null, [Validators.required]),
        ownerId: new FormControl(null, [Validators.required]),
        unitTypeCategoryId: new FormControl(null, [Validators.required]),
        advertisingId: new FormControl(null, [Validators.required]),
      })
    });
    this.odometerReadingFormGroup = this.formBuilder.group({
      odometer: this.formBuilder.group({
        odometerReading: new FormControl(null),
        hours: new FormControl(null)
      })
    });
    this.unitOfDistanceFormGroup = this.formBuilder.group({
      unitOfDistance: this.formBuilder.group({
        id: new FormControl(null),
      })
    });
    this.internetOptionFormGroup = this.formBuilder.group({
      internetOption: this.formBuilder.group({
        displayOnWeb: new FormControl(true),
        priceOnWeb: new FormControl(true)
      }),
    });

    this.lotLocationFormGroup = this.formBuilder.group({
      unitLotLocation: this.formBuilder.group({
        receivingLocationId: new FormControl(activeDealerLocation ?? null, [Validators.required]),
        currentLocationId: new FormControl(activeDealerLocation ?? null, [Validators.required]),
      })
    });

    this.previousOwnerFormGroup = this.formBuilder.group({
      previousOwner: this.formBuilder.group({
        previousOwnerName: new FormControl(null),
        previousOwnerContactId: new FormControl(null, [Validators.required]),
        type: new FormControl(null),
        previousOwnerSupplierId: new FormControl(null),
        previousOwnerVendorId: new FormControl(null)
      })
    });
    this.internetGroupForm = this.formBuilder.group({
      internetGroups: this.formBuilder.group({
        id: new FormControl([])
      })
    });
    this.unitAssociationForm = this.formBuilder.group({
      id: new FormControl(),
      name: new FormControl('')
    });

    if (this.isViewMode) {
      this.generalInfoFormGroup.disable();
      this.odometerReadingFormGroup.disable();
      this.unitOfDistanceFormGroup.disable();
      this.internetOptionFormGroup.disable();
      this.lotLocationFormGroup.disable();
      this.previousOwnerFormGroup.disable();
      this.internetGroupForm.disable();
    }
  }

  private newGeneralInformationFormGroup(): GeneralInformationFormGroup {
    return {
      id: this.isEditMode ? this.inventoryDetails.generalInformation.id : null,
      unitTypeId: this.generalInfoFormGroup.value.generalInformation.unitTypeId,
      year: this.generalInfoFormGroup.value.generalInformation.year,
      vin: this.generalInfoFormGroup.value.generalInformation.vin,
      stockNumber: this.generalInfoFormGroup.value.generalInformation.stockNumber,
      designationId: this.generalInfoFormGroup.value.generalInformation.designationId,
      unitStatusId: this.generalInfoFormGroup.value.generalInformation.unitStatusId,
      makeId: this.generalInfoFormGroup.value.generalInformation.makeId,
      unitModelId: this.generalInfoFormGroup.value.generalInformation.unitModelId,
      ownerId: this.generalInfoFormGroup.value.generalInformation.ownerId,
      unitTypeCategoryId: this.generalInfoFormGroup.value.generalInformation.unitTypeCategoryId,
      advertisingId: this.generalInfoFormGroup.value.generalInformation.advertisingId,
    }
  }

  private newUnitAssociationFormGroup(): UnitAssociation {
    return {
      id: this.isEditMode ? this.inventoryDetails?.unitAssociation?.id : null,
      name: this.isEditMode ? this.inventoryDetails?.unitAssociation?.name : this.generalInfoFormGroup.value.generalInformation.stockNumber
    }
  }

  private newInternetOptionFormGroup(): InternetOptionFormGroup {
    return {
      id: this.isEditMode ? this.inventoryDetails.internetOption.id : null,
      displayOnWeb: this.internetOptionFormGroup.value.internetOption.displayOnWeb,
      priceOnWeb: this.internetOptionFormGroup.value.internetOption.priceOnWeb,
      unitId: this.isEditMode ? this.inventoryDetails.internetOption.unitId : null
    }
  }


  private newPreviousOwnerFormGroup(): PreviousOwnerFormGroup {
    return {
      id: this.isEditMode ? this.inventoryDetails?.previousOwner?.id : null,
      previousOwnerName: this.previousOwnerFormGroup?.value?.previousOwner?.previousOwnerName,
      unitId: this.isEditMode ? this.inventoryDetails?.previousOwner?.unitId : null,
      previousOwnerContactId: this.previousOwnerFormGroup?.value?.previousOwner?.type === ModelType.CONTACT.toUpperCase() ? this.previousOwnerFormGroup?.value?.previousOwner?.previousOwnerContactId?.toString().split('-')[0] : null,
      type: this.previousOwnerFormGroup?.value?.previousOwner?.type,
      previousOwnerVendorId: this.previousOwnerFormGroup?.value?.previousOwner?.type === ModelType.VENDOR.toUpperCase() ? Number(this.previousOwnerFormGroup?.value?.previousOwner?.previousOwnerVendorId?.toString().split('-')[0]) : null,
      previousOwnerSupplierId: this.previousOwnerFormGroup?.value?.previousOwner?.type === ModelType.SUPPLIER.toUpperCase() ? Number(this.previousOwnerFormGroup?.value?.previousOwner?.previousOwnerSupplierId?.toString().split('-')[0]) : null
    }
  }

  private newInternetGroupFormGroup(): InternetGroups[] {
    this.internetGroupInputValues = [];
    this.internetGroup.forEach((groupId) => {
      const found = this.internetGroupCopy.some(item => item.internetGroupId === groupId);
      if (!found) {
        this.internetGroupCopy.push({
          internetGroupId: groupId,
          unitId: this.unitId
        });
      }
    });
    for (const internetGroup of this.internetGroupCopy) {
      if (this.internetGroup.includes(internetGroup.internetGroupId)) {
        this.internetGroupInputValues.push(internetGroup)
      }
    }
    return this.internetGroupInputValues;
  }

  private newOdometerReadingFormGroup(): OdometerReadingFormGroup {
    return {
      id: this.isEditMode ? this.inventoryDetails?.odometer?.id : null,
      odometerReading: this.odometerReadingFormGroup?.value?.odometer?.odometerReading,
      hours: this.odometerReadingFormGroup?.value?.odometer?.hours,
      unitOfDistance: {
        id: Number(this.unitOfDistance)
      },
      unitId: this.isEditMode ? this.inventoryDetails?.odometer?.unitId : null
    }
  }

  private newLotLocationFormGroup(): LotLocationFormGroup {
    return {
      id: this.isEditMode ? this.inventoryDetails.unitLotLocation.id : null,
      receivingLocationId: this.lotLocationFormGroup.value.unitLotLocation.receivingLocationId,
      currentLocationId: this.lotLocationFormGroup.value.unitLotLocation.currentLocationId,
      unitId: this.isEditMode ? this.inventoryDetails.unitLotLocation.unitId : null
    }
  }

  private getInventoryById(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.inventoryInfo || this.inventoryIncomingInfo) {
        this.isLoading = true;
        if (this.inventoryIncomingInfo) {
          this.unitId = this.inventoryIncomingInfo?.unitId
        }
        else {
          this.unitId = this.inventoryInfo?.id;
        }
        if (this.unitId) {
          const endpoint = API_URL_UTIL.inventory.inventoryDetails.replace(':unitId', this.unitId.toString())
          this.inventoryService.get<InventoryGeneralDetails>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
            next: (inventoryDetail) => {
              this.inventoryDetails = inventoryDetail;
              this.setGeneralInformationInFormGroup();
              this.isLoading = false;
              this.inventoryService.setUnitId$(inventoryDetail.id);
              this.cdf.detectChanges();
              resolve();
            },
            error: () => {
              this.isLoading = false;
              this.cdf.detectChanges();
              reject();
            }
          });
        }
      }
      else {
        resolve();
      }
    })
  }

  private getPipelineDetails(): void {
    if (this.inventoryInfo) {
      this.isLoading = true;
      const unitId = this.inventoryInfo?.id;
      if (unitId) {
        const endpoint = API_URL_UTIL.pipelineDetail.unit.replace(':unitId', unitId.toString());
        this.pipelineDetailsService.get<PipelineDetails>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
          next: (pipelineDetail) => {
            this.pipelineDetails = pipelineDetail;
            this.getPipelineDetailsById();
            this.isLoading = false;
            this.cdf.detectChanges();
          },
          error: () => {
            this.isLoading = false;
            this.cdf.detectChanges();
          }
        });
      }
    }
  }

  private getPipelineDetailsById() {
    if (this.pipelineDetails?.id) {
      const endpoint = API_URL_UTIL.pipelineDetail.pipelineDetailById.replace(':id', this.pipelineDetails.id.toString());
      this.pipelineDetailsService.get<SoldTruckBoardListItem>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
        next: (pipelineDetail) => {
          this.selectedTruckBoard = pipelineDetail;
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  setInventoryStatuses(): void {
    if (!this.isEditMode) {
      this.inventoryStatuses.forEach(status => {
        if (status.name === 'Sold' || status.name === 'On Hold') {
          status.isDisabled = true;
        }
      });
    }
  }

  private async setGeneralInformationInFormGroup(): Promise<void> {
    if (this.inventoryDetails) {
      const selectedInternetGroupIds = [];
      for (const internetGroups of this.inventoryDetails.internetGroups) {
        const id = internetGroups.internetGroupId;
        if (id) {
          selectedInternetGroupIds.push(id);
          this.internetGroup.push(internetGroups.internetGroupId);
          this.internetGroupCopy.push(internetGroups);
        }
      }
      this.generalInformation.patchValue({
        unitTypeId: this.inventoryDetails.generalInformation?.unitType?.id,
        year: this.inventoryDetails.generalInformation?.year,
        vin: this.inventoryDetails.generalInformation?.vin,
        stockNumber: this.inventoryDetails.generalInformation?.stockNumber,
        designationId: this.inventoryDetails.generalInformation?.designation.id,
        unitStatusId: this.inventoryDetails.generalInformation?.unitStatus.id,
        makeId: this.inventoryDetails.generalInformation?.make.id,
        unitModelId: this.inventoryDetails.generalInformation?.unitModel.id,
        ownerId: this.inventoryDetails.generalInformation?.owner.id,
        vendorId: this.inventoryDetails.generalInformation?.vendor?.id,
        unitTypeCategoryId: this.inventoryDetails.generalInformation?.unitTypeCategory?.id,
        advertisingId: this.inventoryDetails.generalInformation?.advertising?.id,
      });
      if (this.inventoryDetails.generalInformation.unitTypeCategory.id) {
        this.getUnitTypes(this.inventoryDetails.generalInformation.unitTypeCategory.id)
        this.getMakeList(this.inventoryDetails.generalInformation.unitTypeCategory.id)
        this.categoryId = Number(this.inventoryDetails.generalInformation.unitTypeCategory.id);
      }
      if (this.inventoryDetails.generalInformation?.make.id) {
        this.getModelList(this.inventoryDetails.generalInformation?.make.id)
      }
      this.odometerReading.patchValue({
        odometerReading: this.inventoryDetails.odometer?.odometerReading,
        hours: this.inventoryDetails.odometer?.hours
      });
      this.unitLotLocation.patchValue({
        receivingLocationId: this.inventoryDetails.unitLotLocation?.receivingLocation?.id,
        currentLocationId: this.inventoryDetails.unitLotLocation?.currentLocation?.id,
      });
      this.previousOwner.patchValue({
        previousOwnerName: this.inventoryDetails.previousOwner?.previousOwnerName,
        previousOwnerContactId: this.patchPreviousOwner,
        previousOwnerVendorId: this.patchPreviousOwner,
        previousOwnerSupplierId: this.patchPreviousOwner,
        type: this.inventoryDetails.previousOwner?.type,
      });
      this.internetOption.patchValue({
        displayOnWeb: this.inventoryDetails.internetOption?.displayOnWeb,
        priceOnWeb: this.inventoryDetails.internetOption?.priceOnWeb,
      });
      this.unitOfDistanceValue.patchValue({
        id: this.inventoryDetails.odometer?.unitOfDistance?.id,
      });
      this.internetGroupForm.patchValue({
        internetGroups: {
          id: selectedInternetGroupIds
        }
      });
      this.onCategoryChange.emit(Number(this.inventoryDetails.generalInformation?.unitTypeCategory?.id));
      this.selectedMakeId = Number(this.inventoryDetails.generalInformation?.make.id);
    }
  }

  private get patchPreviousOwner(): string {
    if (this.inventoryDetails.previousOwner?.type === ModelType.CONTACT.toUpperCase()) {
      return this.inventoryDetails.previousOwner?.previousOwnerContactId?.toString()?.concat(`-${ModelType.CONTACT.toUpperCase()}`);
    } else if (this.inventoryDetails.previousOwner?.type === ModelType.VENDOR.toUpperCase()) {
      return this.inventoryDetails.previousOwner?.previousOwnerVendorId?.toString()?.concat(`-${ModelType.VENDOR.toUpperCase()}`);
    } else {
      return this.inventoryDetails.previousOwner?.previousOwnerSupplierId?.toString()?.concat(`-${ModelType.SUPPLIER.toUpperCase()}`);
    }
  }

  get internetGroupFormArray(): FormArray {
    return {
      archived: this.inventoryDetails?.archived ?? false,
      isFinancialAdded: this.isEditMode,
      deleted: false,
      id: this.isEditMode ? this.inventoryDetails.id : null,
      generalInformation: this.newGeneralInformationFormGroup(),
      internetOption: this.newInternetOptionFormGroup(),
      previousOwner: this.newPreviousOwnerFormGroup(),
      ...this.internetGroupForm.value,
      unitLotLocation: this.newLotLocationFormGroup(),
      odometer: this.newOdometerReadingFormGroup(),
      internetGroups: this.newInternetGroupFormGroup(),
      createdDate: this.isEditMode ? this.inventoryDetails.createdDate : new Date(),
      unitAssociation: this.newUnitAssociationFormGroup()
    }
  }

  get generalInformation(): FormGroup {
    return this.generalInfoFormGroup.get('generalInformation') as FormGroup;
  }

  get odometerReading(): FormGroup {
    return this.odometerReadingFormGroup.get('odometer') as FormGroup;
  }

  get unitOfDistanceValue(): FormGroup {
    return this.unitOfDistanceFormGroup.get('unitOfDistance') as FormGroup;
  }

  get unitLotLocation(): FormGroup {
    return this.lotLocationFormGroup.get('unitLotLocation') as FormGroup;
  }

  get previousOwner(): FormGroup {
    return this.previousOwnerFormGroup.get('previousOwner') as FormGroup;
  }

  get internetGroups(): FormGroup {
    return this.internetGroupForm.get('internetGroups') as FormGroup;
  }

  get internetOption(): FormGroup {
    return this.internetOptionFormGroup.get('internetOption') as FormGroup;
  }

  changeCategory(id: number | null) {
    this.generalInfoFormGroup.controls.generalInformation.patchValue({
      unitModelId: null,
      unitTypeId: null,
      makeId: null
    });
    this.models = [];
    this.onCategoryChange.emit(id);
    this.getUnitTypes(id);
    this.getMakeList(id);
    this.categoryId = id;
  }

  changeMake(id: number | null) {
    this.generalInfoFormGroup.controls.generalInformation.patchValue({
      unitModelId: null,
    });
    this.selectedMakeId = id;
    this.getModelList(id)
  }

  private getUnitTypes(categoryId: number | string | null) {
    if (!categoryId) {
      this.unitTypes = [];
    } else {
      this.loaders.unitTypes = true;
      const endpoint = `${API_URL_UTIL.inventory.unitTypesList}?categoryId=${categoryId}`;
      this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
        next: (unitTypes) => {
          this.unitTypes = unitTypes;
          this.loaders.unitTypes = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.loaders.unitTypes = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  private getInventoryStatuses(): void {
    this.loaders.status = true;
    this.inventoryService.getList<IdNameModel>(API_URL_UTIL.inventory.statuses).pipe(takeUntil(this.destroy$)).subscribe({
      next: (inventoryStatuses) => {
        this.inventoryStatuses = inventoryStatuses;
        this.loaders.status = false;
        for (const inventoryStatus of this.inventoryStatuses) {
          if (!this.isEditMode && inventoryStatus.name === 'Available') {
            this.generalInformation.patchValue({ unitStatusId: inventoryStatus.id })
          }
        }
        this.setInventoryStatuses();
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.status = false;
        this.cdf.detectChanges();
      }
    });
  }

  private getMakeList(categoryId: number | string | null): void {
    if (!categoryId) {
      this.makes = [];
      this.models = [];
    } else {
      this.loaders.makes = true;
      this.inventoryService.getCategoryByMakeList(categoryId).pipe(takeUntil(this.destroy$)).subscribe({
        next: (makeListing) => {
          this.makes = makeListing;
          this.loaders.makes = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.loaders.makes = false;
          this.cdf.detectChanges();
        }
      });
    }
  }

  private getModelList(makeId: number | string | null): void {
    if (!makeId) {
      this.models = [];
    } else {
      this.loaders.models = true;
      const endpoint = API_URL_UTIL.inventory.makeBYModelList.replace(':makeId', makeId.toString())
      this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
        next: (models) => {
          this.models = models;
          this.loaders.models = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.loaders.models = false;
          this.cdf.detectChanges();
        }
      })
    }
  }

  private getDesignationList(): void {
    this.loaders.designations = true;
    this.commonService.getList(API_URL_UTIL.designations.root).pipe(takeUntil(this.destroy$)).subscribe({
      next: (designations) => {
        this.designations = designations;
        this.loaders.designations = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.designations = false;
        this.cdf.detectChanges();
      }
    });
  }

  private getVendorList(): void {
    this.loaders.vendors = true;
    this.commonService.getList(API_URL_UTIL.vendorsContactsSuppliers).pipe(takeUntil(this.destroy$)).subscribe({
      next: (vendors: any) => {
        if (typeof (vendors[0].id) === 'number') {
          vendors.map((d: any) => {
            d.name = `${d.name} (${d.type})`;
            d.id = `${d.id}-${d.type}`;
            return d
          })
        }
        this.vendors = vendors;
        this.loaders.vendors = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.vendors = false;
        this.cdf.detectChanges();
      }
    });
  }

  private getInternetGroupList(): void {
    this.loaders.internetGroup = true;
    this.commonService.getList(API_URL_UTIL.internetGroups.root).pipe(takeUntil(this.destroy$)).subscribe({
      next: (internetGroups) => {
        this.internetGroupOptions = internetGroups;
        this.loaders.internetGroup = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.internetGroup = false;
        this.cdf.detectChanges();
      }
    });
  }

  private getDealersList(): void {
    this.loaders.ownedBy = true;
    this.loaders.lotLocation = true;
    this.loaders.currentLocation = true;
    this.dealerService.getDealerList<IdNameModel>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (dealerOptions) => {
        this.dealerOptions = dealerOptions;
        this.loaders.ownedBy = false;
        this.loaders.lotLocation = false;
        this.loaders.currentLocation = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.ownedBy = false;
        this.loaders.lotLocation = false;
        this.loaders.currentLocation = false;
        this.cdf.detectChanges();
      }
    });
  }

  private getContactInfo(): void {
    this.loaders.previousOwnerName = true;
    this.crmService.getList<ContactDetails>(API_URL_UTIL.admin.crm.contact).pipe(takeUntil(this.destroy$)).subscribe({
      next: (contactList) => {
        this.contactList = contactList;
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.previousOwnerName = false;
        this.cdf.detectChanges();
      }
    });
  }

  onSubmit(close = true): void {
    if (this.isViewMode) {
      this.generalInfoFormGroup.enable();
      this.odometerReadingFormGroup.enable();
      this.unitOfDistanceFormGroup.enable();
      this.internetOptionFormGroup.enable();
      this.lotLocationFormGroup.enable();
      this.previousOwnerFormGroup.enable();
      this.internetGroupForm.enable();
      this.isViewMode = false;
      return;
    }

    if (this.generalInfoFormGroup.invalid || this.lotLocationFormGroup.invalid) {
      this.generalInfoFormGroup.markAllAsTouched();
      this.lotLocationFormGroup.markAllAsTouched();
      return;
    }
    this.generalInfoFormGroup.markAsUntouched();
    this.lotLocationFormGroup.markAsUntouched();
    if (this.isEditMode) {
      this.editGeneralInfo(close);
      if (this.soldInventoriesId?.length) {
        this.soldInventories()
      }
      if (this.holdInventoriesId?.length) {
        this.holdInventories()
      }
    }
    else {
      this.saveGeneralInfo(close);
    }
  }

  soldInventories(): void {
    const endpoint = `status/2`;
    const queryParams = `${endpoint}?unitAssociationId=${this.inventoryInfo?.unitAssociationId ?? ''}`
    this.inventoryService.update(this.soldInventoriesId, queryParams).pipe(takeUntil(this.destroy$)).subscribe();
  }

  holdInventories(): void {
    const endpoint = `status/12`;
    const queryParams = `${endpoint}?unitAssociationId=${this.inventoryInfo?.unitAssociationId ?? ''}`
    this.inventoryService.update(this.holdInventoriesId, queryParams).pipe(takeUntil(this.destroy$)).subscribe();
  }

  saveGeneralInfo(close = true): void {
    this.commonSharedService.setBlockUI$(true);
    this.showLoader.emit(true);
    this.generalInfoService.add(this.internetGroupFormArray).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.toasterService.success(MESSAGES.inventoryGeneralAddSuccess);
        this.inventoryService.setUnitId$(res.id);
        this.inventoryService.setAssociateId$(res.unitAssociation.id);
        if (close) {
          this.onClose.emit(true);
          this.commonSharedService.setBlockUI$(false);
          this.showLoader.emit(false);
        }
      }, error: () => {
        this.commonSharedService.setBlockUI$(false);
        this.showLoader.emit(false);
      }
    });
  }

  editGeneralInfo(close = true): void {
    this.commonSharedService.setBlockUI$(true);
    this.showLoader.emit(true);
    this.generalInfoService.update(this.internetGroupFormArray).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: any) => {
        this.toasterService.success(this.isEditMode ? MESSAGES.inventoryGeneralUpdateSuccess : MESSAGES.inventoryGeneralAddSuccess);
        this.inventoryService.setAssociateId$(res.unitAssociation.id)
        if (close) {
          this.onClose.emit(true);
          this.commonSharedService.setBlockUI$(false);
          this.showLoader.emit(false);
        }
      }, error: () => {
        this.commonSharedService.setBlockUI$(false);
        this.showLoader.emit(false);
      }
    });
  }

  onAddEditPopupClose(modelType: string, reload = false): void {
    this.modelPopups.showCreateMake = false;
    this.modelPopups.showAdvertisingBanner = false;
    this.modelPopups.showCreateModel = false;
    this.modelPopups.showCreateVendor = false;
    this.modelPopups.showCreateSupplier = false;
    this.modelPopups.showCreateContact = false;
    this.modelPopups.showCreateUnitType = false;
    this.displaySelectionDialog = false;
    switch (modelType) {
      case ModelType.VENDOR:
      case ModelType.SUPPLIER:
      case ModelType.CONTACT:
        this.getContactInfo();
        this.getVendorList();
        break;
      case ModelType.PIPELINE:
        this.getPipelineDetails();
        break;
      case ModelType.UNIT_TYPE:
        this.getUnitTypes(this.categoryId);
        break;
      case ModelType.MAKE:
        this.getMakeList(this.categoryId);
        break;
      case ModelType.ADVERTISING_CONFIG:
        if (reload) {
          this.getBanners();
        }
        break;
      case ModelType.MODEL:
        this.getModelList(this.selectedMakeId);
        break;
      default:
        break;
    }
    this.inventoryService.setContactFinancialLoaded(true);
  }

  resetForms() {
    this.generalInfoFormGroup.reset();
    this.odometerReadingFormGroup.reset();
    this.unitOfDistanceFormGroup.reset();
    this.internetOptionFormGroup.reset();
    this.lotLocationFormGroup.reset();
    this.previousOwnerFormGroup.reset();
    this.internetGroupForm.reset();
  }

  openModel(modelType: string): void {
    switch (modelType) {
      case ModelType.MAKE:
        const isUnitTypeCategoryIdPresent = !!this.generalInformation.controls.unitTypeCategoryId?.value;
        if (isUnitTypeCategoryIdPresent) {
          this.modelPopups.showCreateMake = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingMake);
        }
        break;
      case ModelType.MODEL:
        const isMakeIdPresent = !!this.generalInformation.controls.makeId?.value;
        if (isMakeIdPresent) {
          this.modelPopups.showCreateModel = true;
        } else {
          this.toasterService.warning(MESSAGES.addMakeBeforeAddingModel);
        }
        break;
      case ModelType.VENDOR:
        this.modelPopups.showCreateVendor = true;
        break;
      case ModelType.SUPPLIER:
        this.modelPopups.showCreateSupplier = true;
        break;
      case ModelType.CONTACT:
        this.modelPopups.showCreateContact = true;
        break;
      case ModelType.ADVERTISING_CONFIG:
        this.modelPopups.showAdvertisingBanner = true;
        break;
      case ModelType.UNIT_TYPE:
        if (this.generalInformation.controls.unitTypeCategoryId?.value) {
          this.modelPopups.showCreateUnitType = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingUnitType)
        }
        break;
      default:
        break;
    }
  }

  displayContactDetails(event: any): void {
    if (event) {
      const selectedContact:IdNameModel = this.vendors.find(vendor => vendor.id === event.value);
      if (selectedContact && selectedContact.archived) {
        this.previousOwnerFormGroup.controls['previousOwner']['controls']['previousOwnerContactId'].setValue(null)
        this.toasterService.warning(MESSAGES.archivedContact);
        this.displayContact = null;
        return;
      }
    }
    const selectedText = event.originalEvent.srcElement.innerText;
    const type = selectedText && selectedText.split('(')[1].split(')')[0].toUpperCase();
    const resetPreviousOwner = () => {
      this.previousOwnerFormGroup.value.previousOwner.type = null;
      this.previousOwnerFormGroup.value.previousOwner.previousOwnerContactId = null;
      this.previousOwnerFormGroup.value.previousOwner.previousOwnerVendorId = null;
      this.previousOwnerFormGroup.value.previousOwner.previousOwnerSupplierId = null;
      this.displayContact = null;
    };
    resetPreviousOwner();
    if (!event.value || !type) {
      this.cdf.detectChanges();
      return;
    }
    const id = Number(event.value.split('-')[0]);
    switch (type) {
      case ModelType.CONTACT.toUpperCase():
        this.previousOwnerFormGroup.value.previousOwner.type = ModelType.CONTACT.toUpperCase();
        this.previousOwnerFormGroup.value.previousOwner.previousOwnerContactId = event.value;
        this.displayContact = this.contactList.find(contact => contact.id === id);
        break;

      case ModelType.VENDOR.toUpperCase():
        this.previousOwnerFormGroup.value.previousOwner.type = ModelType.VENDOR.toUpperCase();
        this.previousOwnerFormGroup.value.previousOwner.previousOwnerVendorId = event.value;
        this.displayContact = this.vendors.find(vendor => vendor.id === id);
        break;

      case ModelType.SUPPLIER.toUpperCase():
        this.previousOwnerFormGroup.value.previousOwner.type = ModelType.SUPPLIER.toUpperCase();
        this.previousOwnerFormGroup.value.previousOwner.previousOwnerSupplierId = event.value;
        this.displayContact = this.vendors.find(supplier => supplier.id === id);
        break;

      default:
        resetPreviousOwner();
        break;
    }
    this.cdf.detectChanges();
  }

  changeStatus(id: number): void {
    const SOLD_STATUS_ID = 2
    const HOLD_STATUS_ID = 12
    const isSoldStatus = id === SOLD_STATUS_ID;
    const isHoldStatus = id === HOLD_STATUS_ID;
    const hasAssociations = Boolean(this.inventoryInfo?.associations?.length);

    const currentStatus = this.currentInventoryStatus ? this.currentInventoryStatus : this.inventoryDetails.generalInformation.unitStatus.id;

    if (currentStatus === SOLD_STATUS_ID && id !== 1) {
      this.toasterService.error(MESSAGES.onlyToAvailable);
      setTimeout(() => {
        this.generalInformation.controls.unitStatusId.setValue(2);
        this.currentInventoryStatus = 2;
      }, 0);
      return;
    }
    if (currentStatus === HOLD_STATUS_ID && id !== 1) {
      this.toasterService.error(MESSAGES.onlyToAvailable);
      setTimeout(() => {
        this.generalInformation.controls.unitStatusId.setValue(12);
        this.currentInventoryStatus = 12;
      }, 0);
      return;
    }
    if (currentStatus === SOLD_STATUS_ID && id === 1) {
      this.currentInventoryStatus = 1;
      this.showSoldTab.next(false);
      return;
    }
    if (currentStatus === HOLD_STATUS_ID && id === 1) {
      this.currentInventoryStatus = 1;
      this.showHoldTab.next(false);
      return;
    }

    if (isSoldStatus) {
      if (hasAssociations) {
        this.modelPopups.showInventoryToBeSoldModal = true;
        this.selectedInventoryInfo = this.inventoryInfo ?? {} as InventoryListItem;
      } else if (this.isEditMode) {
        this.currentInventoryStatus = 2;
        this.showSoldTab.next(true);
      }
    }
    if (isHoldStatus) {
      if (hasAssociations) {
        this.modelPopups.showInventoryToBeHoldModal = true;
        this.selectedInventoryInfo = this.inventoryInfo ?? {} as InventoryListItem;
      } else if (this.isEditMode) {
        this.currentInventoryStatus = 12;
        this.showHoldTab.next(true);
      }
    }
  }

  // NOTE: The following modules are currently disabled per client's request.
  // showPipelineDetails(): void {
  //   if (this.pipelineDetails.pipelineType === PipelineTypeList.soldPipelineType) {
  //     this.modelPopups.showSoldPipelineDetails = true;
  //   }
  //   else {
  //     this.modelPopups.showStockPipelineDetails = true;
  //   }
  // }

  toggleGoogleMapPopUp(contact: ContactDetails): void {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress(contact)
  }

  getFullAddress(contact: ContactDetails): void {
    const rowAddress = []
    rowAddress.push(contact?.streetAddress ? contact.streetAddress : '')
    rowAddress.push(contact?.city ? contact.city : '')
    rowAddress.push(contact?.state ? contact.state : '')
    rowAddress.push(contact?.zipcode ? contact.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }

  private getCategoryTypes(): void {
    this.loaders.categoryType = true;
    this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
      next: (categoryTypes: any) => {
        this.categoryTypes = categoryTypes;
        if (this.categoriesToShow?.length) {
          this.categoryTypes = this.categoriesToShow;
        }
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      }
    });
  }

  showDialog(): void {
    this.displaySelectionDialog = true;
  }

  onSoldModalClose(isClose: boolean): void {
    this.modelPopups.showInventoryToBeSoldModal = false;
    this.selectedInventoryInfo = {} as InventoryListItem;
  }

  onHoldModalClose(isClose: boolean): void {
    this.modelPopups.showInventoryToBeHoldModal = false;
    this.selectedInventoryInfo = {} as InventoryListItem;
  }

  setSoldInventoriesId(soldInventoriesId: Array<number>): void {
    this.soldInventoriesId = soldInventoriesId;
    this.showSoldTab.next(true);
  }

  setHoldInventoriesId(holdInventoriesId: Array<number>): void {
    this.holdInventoriesId = holdInventoriesId;
    this.showHoldTab.next(true);
  }

  setLocation(ownerId: number): void {
    if (ownerId && !this.inventoryInfo?.id) {
      this.unitLotLocation.patchValue({
        receivingLocationId: ownerId,
        currentLocationId: ownerId
      })
    }
  }

  private getBanners(): void {
    this.banners = [];
    this.loaders.advertising = true;
    this.advertisingService.getList<BannerListItem>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (res: BannerListItem[]) => {
        this.banners = res;
        if (!(this.isEditMode || this.isViewMode)) {
          this.generalInfoFormGroup.controls.generalInformation.patchValue({ advertisingId: this.banners[1].id })
        }
        this.loaders.advertising = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.advertising = false;
        this.cdf.detectChanges();
      }
    })
  }

  updateDisplayOnWeb(displayOnWeb: boolean): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: displayOnWeb ? MESSAGES.updateDisplayOnWebWarning.replace('{action}', 'want to') : MESSAGES.updateDisplayOnWebWarning.replace('{action}', 'dont want to'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDisplayOnWebConfirmation(displayOnWeb);
      },
      reject: () => {
        this.internetOption.patchValue({
          displayOnWeb: this.inventoryDetails.internetOption?.displayOnWeb
        });
        this.cdf.detectChanges();
      }
    });
  }

  private onDisplayOnWebConfirmation(displayOnWeb: boolean): void {
    const endpoint = `${API_URL_UTIL.inventory.internetOptions}`;
    const requestParams = [{
      id: Number(this.inventoryInfo?.generalInformation.id || this.inventoryIncomingInfo?.unit.generalInformation.id),
      value: displayOnWeb
    }]
    this.generalInfoService.updateDisplayOnWeb(requestParams, endpoint).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.toasterService.success(displayOnWeb ? MESSAGES.updateDisplayOnWebSuccess.replace('{action}', 'will be') : MESSAGES.updateDisplayOnWebSuccess.replace('{action}', 'will not be'));
        }
      });
  }

  isOptionDisabled(option: { id: number; archived: boolean }): boolean {
    const selectedValue = this.previousOwnerFormGroup?.get('previousOwner.previousOwnerContactId')?.value;
    return option.archived && option.id !== selectedValue;
  }
}

