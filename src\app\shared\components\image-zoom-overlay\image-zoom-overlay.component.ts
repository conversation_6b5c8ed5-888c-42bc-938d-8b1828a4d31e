import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faIcons } from '../../../@core/utils/font-awesome-icon.utils';

export interface ZoomImage {
  fullUrl: string;
  alt?: string;
}

@Component({
  selector: 'app-image-zoom-overlay',
  standalone: true,
  imports: [CommonModule, FontAwesomeModule],
  templateUrl: './image-zoom-overlay.component.html',
  styleUrls: ['./image-zoom-overlay.component.scss'],
})
export class ImageZoomOverlayComponent implements OnInit, OnDestroy {
  @Input() images: ZoomImage[] = [];
  @Input() selectedIndex: number = 0;
  @Input() isVisible: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() indexChange = new EventEmitter<number>();

  @ViewChild('zoomImage', { static: false }) zoomImageRef!: ElementRef<HTMLImageElement>;

  // Zoom state
  zoomLevel: number = 1;
  minZoom: number = 0.5;
  maxZoom: number = 5;
  panX: number = 0;
  panY: number = 0;

  // Pan state
  isPanning: boolean = false;
  lastPanX: number = 0;
  lastPanY: number = 0;

  // Help and hints state
  showHelp: boolean = false;
  showFeatureHints: boolean = true;
  hasUserPanned: boolean = false;
  hasUserNavigated: boolean = false;
  hasUserZoomed: boolean = false;
  hintsShownCount: { [key: string]: number } = {};
  maxHintShowCount: number = 3; // Show each hint maximum 3 times (increased for testing)
  hintTimeout: any;

  faIcons = faIcons;
  Math = Math;

  ngOnInit(): void {
    if (this.isVisible) {
      document.body.style.overflow = 'hidden';
      this.resetHintsState();
      this.hideFeatureHintsAfterDelay();
    }
  }

  ngOnChanges(changes: any): void {
    if (changes.isVisible && changes.isVisible.currentValue) {
      console.log('Overlay became visible, resetting hints');
      this.resetHintsState();
      this.hideFeatureHintsAfterDelay();
    }
  }

  @HostListener('document:keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.isVisible) return;

    switch (event.key) {
      case 'Escape':
        this.closeOverlay();
        break;
      case 'ArrowLeft':
        this.navigateImage('prev');
        break;
      case 'ArrowRight':
        this.navigateImage('next');
        break;
      case '+':
      case '=':
        this.zoomIn();
        break;
      case '-':
        this.zoomOut();
        break;
      case '0':
        this.resetZoom();
        break;
      case '?':
      case 'h':
      case 'H':
        this.toggleHelp();
        break;
    }
  }

  closeOverlay(): void {
    this.isVisible = false;
    this.resetZoomState();
    document.body.style.overflow = 'auto';
    this.close.emit();
  }

  navigateImage(direction: 'prev' | 'next'): void {
    if (direction === 'prev' && this.selectedIndex > 0) {
      this.selectedIndex--;
      this.hasUserNavigated = true;
      this.dismissHintsAfterAction();
    } else if (direction === 'next' && this.selectedIndex < this.images.length - 1) {
      this.selectedIndex++;
      this.hasUserNavigated = true;
      this.dismissHintsAfterAction();
    }
    this.indexChange.emit(this.selectedIndex);
    this.resetZoomState();
  }

  getCurrentImage(): ZoomImage | undefined {
    return this.images[this.selectedIndex];
  }

  // Zoom Control Methods
  zoomIn(): void {
    if (this.zoomLevel < this.maxZoom) {
      this.zoomLevel = Math.min(this.zoomLevel * 1.2, this.maxZoom);
      if (!this.hasUserZoomed) {
        this.hasUserZoomed = true;
        this.dismissHintsAfterAction();
      }
    }
  }

  zoomOut(): void {
    if (this.zoomLevel > this.minZoom) {
      this.zoomLevel = Math.max(this.zoomLevel / 1.2, this.minZoom);
      if (!this.hasUserZoomed) {
        this.hasUserZoomed = true;
        this.dismissHintsAfterAction();
      }
      if (this.zoomLevel <= 1) {
        this.panX = 0;
        this.panY = 0;
      }
    }
  }

  resetZoom(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
  }

  onWheel(event: WheelEvent): void {
    event.preventDefault();
    if (event.deltaY < 0) {
      this.zoomIn();
    } else {
      this.zoomOut();
    }
    if (!this.hasUserZoomed) {
      this.hasUserZoomed = true;
      this.dismissHintsAfterAction();
    }
  }

  // Pan Methods
  startPan(event: MouseEvent): void {
    if (this.zoomLevel <= 1) return;

    this.isPanning = true;
    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
    event.preventDefault();
  }

  onPan(event: MouseEvent): void {
    if (!this.isPanning || this.zoomLevel <= 1) return;

    const deltaX = event.clientX - this.lastPanX;
    const deltaY = event.clientY - this.lastPanY;

    this.panX += deltaX;
    this.panY += deltaY;

    if (!this.hasUserPanned) {
      this.hasUserPanned = true;
      this.dismissHintsAfterAction();
    }

    this.lastPanX = event.clientX;
    this.lastPanY = event.clientY;
  }

  endPan(): void {
    this.isPanning = false;
  }

  // Helper Methods
  getImageTransform(): string {
    return `scale(${this.zoomLevel}) translate(${this.panX / this.zoomLevel}px, ${this.panY / this.zoomLevel}px)`;
  }

  onImageLoad(): void {
    // Reset zoom state when new image loads
    this.resetZoomState();
  }

  private resetZoomState(): void {
    this.zoomLevel = 1;
    this.panX = 0;
    this.panY = 0;
    this.isPanning = false;
    this.hasUserPanned = false;
  }

  // Help and tooltip methods
  toggleHelp(): void {
    this.showHelp = !this.showHelp;
  }

  getImageContainerTooltip(): string {
    if (this.zoomLevel > 1) {
      return 'Click and drag to pan around the image. Use mouse wheel to zoom.';
    }
    return 'Use mouse wheel to zoom in/out. Click zoom buttons for controls.';
  }

  // Auto-hide feature hints after user interaction
  private hideFeatureHintsAfterDelay(): void {
    if (this.hintTimeout) {
      clearTimeout(this.hintTimeout);
    }
    this.hintTimeout = setTimeout(() => {
      this.showFeatureHints = false;
    }, 5000); // Hide after 5 seconds (reduced from 10)
  }

  // Reset hints state when overlay opens
  private resetHintsState(): void {
    // Only show hints if user hasn't seen them too many times
    this.showFeatureHints = this.shouldShowHints();
    this.hasUserPanned = false;
    this.hasUserNavigated = false;
    this.hasUserZoomed = false;

    // Debug logging
    console.log('Hints state reset:', {
      showFeatureHints: this.showFeatureHints,
      hintsShownCount: this.hintsShownCount,
      zoomLevel: this.zoomLevel,
      imagesLength: this.images.length
    });
  }

  // Check if we should show hints based on usage count
  private shouldShowHints(): boolean {
    const totalHintsShown = Object.values(this.hintsShownCount).reduce((sum, count) => sum + count, 0);
    console.log('Total hints shown so far:', totalHintsShown);
    return totalHintsShown < 10; // Stop showing hints after 10 total hints shown (increased for testing)
  }

  // Check if hint should be displayed (without side effects)
  shouldShowHint(hintType: string): boolean {
    if (!this.showFeatureHints) return false;

    const count = this.hintsShownCount[hintType] || 0;
    return count < this.maxHintShowCount;
  }

  // Track that a hint was actually shown (call this when hint appears)
  markHintAsShown(hintType: string): void {
    const count = this.hintsShownCount[hintType] || 0;
    this.hintsShownCount[hintType] = count + 1;

    // Auto-hide hints after showing any hint
    this.hideFeatureHintsAfterDelay();
  }

  // Specific hint display methods
  showZoomHint(): boolean {
    const shouldShow = this.zoomLevel <= 1 && !this.hasUserZoomed && this.shouldShowHint('zoom');
    console.log('Zoom hint check:', {
      zoomLevel: this.zoomLevel,
      hasUserZoomed: this.hasUserZoomed,
      shouldShowHint: this.shouldShowHint('zoom'),
      showFeatureHints: this.showFeatureHints,
      shouldShow,
      isVisible: this.isVisible
    });
    if (shouldShow) {
      console.log('Marking zoom hint as shown');
      this.markHintAsShown('zoom');
    }
    return shouldShow;
  }

  showPanHint(): boolean {
    const shouldShow = this.zoomLevel > 1 && !this.hasUserPanned && this.shouldShowHint('pan');
    if (shouldShow) {
      this.markHintAsShown('pan');
    }
    return shouldShow;
  }

  showNavigateHint(): boolean {
    const shouldShow = this.images.length > 1 && !this.hasUserNavigated && this.shouldShowHint('navigate');
    if (shouldShow) {
      this.markHintAsShown('navigate');
    }
    return shouldShow;
  }

  // Dismiss hints immediately after user action
  private dismissHintsAfterAction(): void {
    setTimeout(() => {
      this.showFeatureHints = false;
    }, 1000); // Hide hints 1 second after user action
  }

  ngOnDestroy(): void {
    document.body.style.overflow = 'auto';
    if (this.hintTimeout) {
      clearTimeout(this.hintTimeout);
    }
  }
}
