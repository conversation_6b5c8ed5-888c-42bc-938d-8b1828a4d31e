export const Constants = {
  applicationLogoUrl: 'assets/images/logo.svg',
  applicationLogoDarkUrl: 'assets/images/logo_dark.png',
  regexForOnlyNumberCheck: /^\d+$/,
  environmentsForErrorTracing: ['localhost', 'https://dev.baseprojectangular.thesunflowerlab.com/'],
  splashScreenTimeout: 200,
  dateFormat: 'MM/dd/yyyy',
  fullDateFormat: 'MM/dd/yyyy hh:mm a',
  monthAndDateFormat: 'MMM dd',
  monthYear: 'MMM yy',
  monthDateAndYearFormat: 'MMM dd, yyyy',
  time: 'hh:mm a',
  phoneNumberMask: '(*************',
  phoneNumberMaskForTemplateForm: '(*************',
  fileSize: 10000000,
  totalFileSize: 50000000,
  regexForEmailValidation: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  regexForSpecialCharacters: /[^a-zA-Z0-9._\-\s]/g,
  maxPriceAmount: 750000,
  archived: '(Archived)',
  staticImages: {
    truckImage: 'assets/images/truck_image.png',
    noImages: 'assets/images/comming-soon.png',
    icons: {
      crmActive: 'assets/images/icons/CRM-Active.svg',
      crmHover: 'assets/images/icons/CRM-Hover.svg',
      adminActive: 'assets/images/icons/Administrator-Active.svg',
      adminHover: 'assets/images/icons/Administrator-Hover.svg',
      reportActive: 'assets/images/icons/bar-chart-active.svg',
      reportHover: 'assets/images/icons/bar-chart-hover.svg',
      dashboardActive: 'assets/images/icons/Dashboard-Active.svg',
      dashboardHover: 'assets/images/icons/Dashboard-Hover.svg',
      inventoryActive: 'assets/images/icons/Inventory-Active.svg',
      inventoryHover: 'assets/images/icons/Inventory-Hover.svg',
      transportActive: 'assets/images/icons/Transport-Active.svg',
      transportHover: 'assets/images/icons/Transport-Hover.svg',
      pipelineActive: 'assets/images/icons/Pipeline-Active.svg',
      pipelineHover: 'assets/images/icons/Pipeline-Hover.svg',
      uploadArrow: 'assets/images/icons/Upload-Arrow.svg',
      uploadImage: 'assets/images/icons/Upload-Image.svg',
      viewIcon: 'assets/images/icons/View-Icon.svg',
      deleteIcon: 'assets/images/icons/Delete-Icon.svg',
      deliveryTruck: 'assets/images/icons/Delivery-Truck.svg',
      addNew: 'assets/images/icons/Add-New.svg',
      add: 'assets/images/icons/Add.svg',
      minus: 'assets/images/icons/Minus.svg',
      addNewHover: 'assets/images/icons/Add-New-Hover.svg',
      bellNotification: 'assets/images/icons/Bell-Notification.svg',
      download: 'assets/images/icons/Download.svg',
      edit: 'assets/images/icons/Edit.svg',
      clone: 'assets/images/icons/clone-regular.svg',
      exportFile: 'assets/images/icons/Export-File.svg',
      recentlyAdded: 'assets/images/icons/recently-added.svg',
      key: 'assets/images/icons/key.svg',
      lockPassword: 'assets/images/icons/Lock-Password.svg',
      loginUser: 'assets/images/icons/Login-User.svg',
      timeClock: 'assets/images/icons/Time-Clock.svg',
      viewExpense: 'assets/images/icons/view-expense.svg',
      shopActive: 'assets/images/icons/Shop-Active.svg',
      shopHover: 'assets/images/icons/Shop-Hover.svg',
      addImage: 'assets/images/icons/Add-Image.svg',
      editImage: 'assets/images/icons/Edit-Images.svg',
      contact: 'assets/images/icons/Contact.svg',
      map: 'assets/images/icons/Map.svg',
      photoAdd: 'assets/images/icons/Photo-add.svg',
      photoView: 'assets/images/icons/Photo-view.svg',
      pdfIcon: 'assets/images/icons/Pdf-icon.svg',
      share: 'assets/images/icons/share.svg',
      shareIcon: 'assets/images/icons/share-icon.svg',
      expenseContract: 'assets/images/icons/noun-contract.svg',
      dollar: 'assets/images/icons/dollar.svg',
      sale: 'assets/images/icons/sales.svg',
      truckSale: 'assets/images/icons/truck-sale.svg',
      truck: 'assets/images/icons/truck.svg',
      inventory: 'assets/images/icons/inventory.svg',
      truckBlack: 'assets/images/icons/Truck-Black.svg',
      circleCheckGreen: 'assets/images/icons/Cirlce-Check-Green.png',
      resetInput: 'assets/images/icons/Clear-filter3x.svg',
      userXmark: 'assets/images/icons/user-xmark.svg',
      calender: 'assets/images/icons/Calender.svg',
      circle: 'assets/images/icons/circle.svg',
      linkOff: 'assets/images/icons/Link-Off.svg',
      GetQuote: 'assets/images/icons/Get-Quote.svg',
      white404Icon: 'assets/images/icons/white-404-icon.svg',
      black404Icon: 'assets/images/icons/black-404-icon.svg',
    }
  },
  globalModalConfig: {
    // add modal global configs here
  },
  sidebarModalConfig: {
    class: 'right',
  },
  allowedImgFormats: ".jpg, .png, .gif, .jpeg",
  allowedPdfFormats: '.pdf',
  allowedImgAndOdfFormats: ".jpg, .png, .gif, .jpeg, .pdf",
  firebase: {
    registrationToken: 'firebaseRegistrationToken'
  },
  infiniteScrollConfig: {
    infiniteScrollThrottle: 0,
    infiniteScrollDistance: 2,
    fromRoot: true,
  },
  weekDays: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
  tooltip: {
    resetFilters: 'Reset Filters'
  },
  localStorageConstants: {
    roleInfo: 'roleInfo'
  }
}

export const Status = {
  inProgress: 'In Progress',
  done: 'Done',
  completed: 'Completed'
}

export const IncomingTruckTabError = {
  warning: "Please add required details in {tab} tab before saving.",
  fillPaymentTabInfo: "Please add Payment tab information to send email.",
  documentIsNecessary: "Please add Document in Acquisition tab to send email.",
  emailWarning: "Please add {data} to send email."
}

export const PipelineTypeList = {
  soldPipelineType: 'SOLD',
  stockPipelineType: 'STOCK'
}

export const SoldStockPipelineStatusList = {
  waiting_on_parts: 'WAITING_ON_PARTS',
  completed: 'COMPLETED',
  in_staging: 'IN_STAGING',
  schedule: 'SCHEDULE'
}

export const returnPipelineStatusList = {
  waiting_on_parts: 'Waiting On Parts',
  completed: 'Completed',
  in_staging: 'In Staging',
  schedule: 'Schedule'
}

export const progressColorList = {
  red: 'red',
  green: 'green',
  blue: 'blue',
  placeholder: 'placeholder'
}

export const roles = {
  salesPerson: 'ROLE_SALESPERSON',
  driver: 'ROLE_DRIVER'
}

export const driverAddress = {
  pickUp: 'PICK_UP',
  destination: 'DESTINATION'
}

export const icons = {
  triangle: 'pi pi-exclamation-triangle',
  angleDoubleUp: 'pi-angle-double-up',
  angleUp: "pi-angle-up",
  bars: "pi-bars",
  angleDown: "pi-angle-down"
}

export const dateFormat = {
  format: 'yyyy-MM-dd'
}

export const Reminders = {
  ActiveReminders: 'Active Reminders'
}

export const driverScheduleStatus = {
  scheduled: 'Scheduled',
  requested: 'Requested',
  completed: 'Completed'
}

export const CustomerLeadStatusList = {
  open: 'OPEN',
  closed: 'CLOSE',
  deal: 'DEAL'
}

export const returnCustomerLeadStatusList = {
  open: 'Open',
  closed: 'Close',
  deal: 'Deal'
}

export const HistoryModuleActions = {
  CREATE: "CREATE",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
  ARCHIVED: "ARCHIVED",
  UNARCHIVED: 'UNARCHIVED',
  MOVED: 'MOVED',
}

export const RegExp = {
  BlockSpecialCharAllowSpace: /^[a-zA-Z0-9\s]*$/,
}

export const HistoryModuleName = {
  CUSTOMER_LEAD: 'CUSTOMER_LEAD',
  CRM_CONTACT: 'CRM_CONTACT',
  TASK_SALES: 'TASK_SALES',
  INCOMING_TRUCK: 'INCOMING_TRUCK',
  DRIVER_SCHEDULE: 'DRIVER_SCHEDULE',
  INVENTORY: 'INVENTORY',
  PIPELINE_SOLD: 'PIPELINE_SOLD',
  PIPELINE_STOCK: 'PIPELINE_STOCK',
  SHOPS: 'TASK',
  USERS: 'USER',
  DEALERS: 'DEALER',
  VENDORS: 'VENDOR',
  SUPPLIERS: 'SUPPLIER',
  PIPELINE_CONFIG: 'PIPELINE_CONFIG',
  SHOP: 'SHOP',
  ROLES_AND_PERMISSION: 'ROLES_AND_PERMISSION',
  CATEGORY: 'CATEGORY',
  ADVERTISING: 'ADVERTISING',
}

export const HistorySearchPlaceholder = {
  LEAD_ID: 'Lead Id',
  CONTACT_NAME: 'Contact Name',
  TASK_ID: 'Task Id',
  VIN_NUMBER: 'Vin Number',
  ITEM_NAME: 'Item Name',
  STOCK_NUMBER: 'Stock Number',
  NAME: 'Name',
  TITLE: 'Title',
}

export const specificationDetails = 'specificationDetails';

export const TASK_TYPES = {
  GENERAL: 1,
  SOLD_PIPELINE: 2,
  STOCK_PIPELINE: 3,
  SALES: 4,
}

export const DASHBOARD_REDIRECT = {
  DAILY_SALES: 'Daily Sales',
  MONTHLY_SALES: 'Monthly Sales',
  TOTAL_INVENTORY_COUNT: 'Total Inventory Count',
  INCOMING_TRUCK_COUNT: 'Incoming Truck Count'
}