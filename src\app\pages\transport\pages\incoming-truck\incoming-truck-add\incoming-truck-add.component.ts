import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { IncomingTruckTabError, MESSAGES, icons } from '@constants/*';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { Account } from '@pages/auth/models';
import { AuthService } from '@pages/auth/services/auth.service';
import { CrmService } from '@pages/crm/services/crm.service';
import { ExpensesAttachment, PreviousOwner } from '@pages/inventory/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { UserAnnotationService } from '@pages/inventory/services/user-annotation.service';
import { SoldTruckService } from '@pages/pipeline/pages/sold-truck-board/sold-truck.service';
import { IncomingTruckBoardListItem, IncomingTruckCreateParams, IncomingTruckDetails, IncomingTruckStatusList, ModelType } from '@pages/transport/models/incoming-truck.model';
import { IncomingTruckService } from '@pages/transport/services/incoming-truck.service';
import { User } from '@pages/user/models';
import * as saveAs from 'file-saver';
import { Address } from 'ngx-google-places-autocomplete/objects/address';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel } from 'src/app/@shared/models';
import { CommonService } from 'src/app/@shared/services/common.service';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';
import { AcquisitionInfoComponent } from './acquisition-info/acquisition-info.component';

@Component({
  selector: 'app-incoming-truck-add',
  templateUrl: './incoming-truck-add.component.html',
  styleUrls: ['./incoming-truck-add.component.scss'],
  providers: [ConfirmationService]
})
export class IncomingTruckAddComponent extends BaseComponent implements OnInit, OnChanges {

  @Input() incomingTruckInfo!: IncomingTruckBoardListItem | null;
  @Input() categoriesToDisplay!: IdNameModel[];
  title = 'Add Incoming Truck';
  @Output() isPrevious: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onClose: EventEmitter<void> = new EventEmitter<void>();
  @Output() hasBeenModified: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() isEditTitle: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild(AcquisitionInfoComponent) acquisitionInfoComponent!: AcquisitionInfoComponent;
  ACQUISITION = 'Acquisition Info';
  PAYMENT = 'Payment Processing';
  PICKUP = 'Pickup Details';
  saveLoader = false;
  saveAndAddLoader = false;
  incomingTruckFormGroup!: FormGroup;
  displayBasic = false;
  message = '';
  isEditMode = false;
  users!: User[];
  showIncomingCreateModal = false;
  currentUser!: Account | null;
  acquisitionType: IdNameModel[] = [];
  vendorList: IdNameModel[] = [];
  showAttachmentsTab = true;
  incomingTruckHasArrived = false;
  categoryTypes: IdNameModel[] = [];
  categoriesToShow: IdNameModel[] = [];
  inventoryStatuses = IncomingTruckStatusList;
  status: string | undefined = IncomingTruckStatusList[0].value;
  pickupNotes!: string;
  enablePaymentTab!: boolean;
  enablePickupTab!: boolean;
  isStatusError = false;
  loaders = {
    makes: false,
    models: false,
    categories: false,
    years: false,
    status: false,
    unitTypes: false,
    designations: false,
    vendors: false,
    stockNumbers: false,
    acquisitionType: false,
    lotLocation: false,
    currentLocation: false,
    ownedBy: false,
    categoryType: false,
    purchaseBy: false,
    previousOwnerName: false,
    users: false
  };
  acquisitionFile!: File;

  @Input() isViewMode!: boolean;
  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'Expenses Files';
  makes: IdNameModel[] = [];
  models: IdNameModel[] = [];
  categories: IdNameModel[] = [];
  years: IdNameModel[] = [];
  statuses: IdNameModel[] = [];
  unitTypes: IdNameModel[] = [];
  designations: IdNameModel[] = [];
  vendors: IdNameModel[] = [];
  dealerOptions: IdNameModel[] = [];
  purchaseByList: IdNameModel[] = [];
  isUploadFlag = true;
  incomingTruckDetails!: IncomingTruckDetails | null;
  showGoogleMapSideBar = false;
  options: any = {
    componentRestrictions: { country: 'US' }
  }
  fullAddress!: string;
  selectedCategoryId!: number;
  categoryId!: number;
  selectedMakeId!: number;
  activeIndex = 0;

  initialFormValues = {
    unitDTO: this.unitDtoFormGroup.value,
    driverScheduleAddressDTO: this.newAddressFormGroupPickup.value,
    incomingTruckExpenseDTO: this.newExpensesFormGroup.value,
    paymentProcessingDTO: this.newPaymentProcessingFormGroup.value
  };

  constructor(private readonly fb: FormBuilder,
    private readonly soldTruckService: SoldTruckService,
    private readonly commonSharedService: CommonSharedService,
    private readonly confirmationService: ConfirmationService,
    private readonly authService: AuthService,
    private readonly dealerService: DealerService,
    private readonly commonService: CommonService,
    private readonly inventoryService: InventoryService,
    private readonly fileUploadService: FileUploadService,
    private readonly toasterService: AppToasterService,
    private readonly cdf: ChangeDetectorRef,
    private readonly expensesService: ExpensesService,
    private readonly incomingTruckService: IncomingTruckService,
    private readonly crmService: CrmService,
    private readonly userAnnotationService: UserAnnotationService) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.commonSharedService.setBlockUI$(true);
    this.initializeFormGroup();
    await this.getAll();
    this.getCurrentUser();
    await this.getIncomingTruckById();
    this.commonSharedService.setBlockUI$(false);
    if (this.isViewMode) {
      this.hideShowAttachmentsTab();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.incomingTruckInfo?.currentValue && !this.isViewMode) {
      this.isEditMode = true;
    }
  }

  private async getAll() {
    await Promise.all([
      this.getDesignationList(),
      this.getVendorList(),
      this.getDealersList(),
      this.getAcquisitionList(),
      this.getCategoryTypes(),
      this.getPurchaseByName(),
      this.getUsers()
    ]);
  }

  private getUsers(): void {
    this.loaders.users = true;
    this.userAnnotationService.get<User[]>(API_URL_UTIL.userAnnotation.userFilter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: User[]) => {
          this.users = res;
          this.loaders.users = false;
        },
        error: () => {
          this.loaders.users = false;
        }
      }
      );
  }
  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
      }
    });
  }

  private getIncomingTruckById(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.incomingTruckInfo) {
        const unitId = this.incomingTruckInfo?.id;
        if (unitId) {
          this.isLoading = true;
          this.incomingTruckService.get<IncomingTruckDetails>(unitId.toString()).pipe(takeUntil(this.destroy$)).subscribe({
            next: (incomingTruckDetails) => {
              this.incomingTruckDetails = incomingTruckDetails;
              this.incomingTruckHasArrived = incomingTruckDetails.status === "ARRIVED"
              this.setIncomingTruckInfoFormGroup();
              this.isLoading = false;
              resolve();
            },
            error: () => {
              this.isLoading = false;
              this.cdf.detectChanges();
              reject();
            }
          });
        }
      }
      else {
        resolve();
      }
    })
  }

  private initializeFormGroup(): void {
    this.incomingTruckFormGroup = this.fb.group({
      unitDTO: this.unitDtoFormGroup,
      driverScheduleAddressDTO: this.newAddressFormGroupPickup,
      incomingTruckExpenseDTO: this.newExpensesFormGroup,
      paymentProcessingDTO: this.newPaymentProcessingFormGroup
    });
  }

  get expensesAttachments(): ExpensesAttachment {
    let expensesAttachments: ExpensesAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl, fileName: "" }));
    if (this.isEditMode && this.incomingTruckInfo?.incomingTruckExpenseDTO?.expensesAttachmentDTO?.url) {
      expensesAttachments = [this.incomingTruckInfo.incomingTruckExpenseDTO.expensesAttachmentDTO, ...expensesAttachments];
    }
    return expensesAttachments[0];
  }

  get pickUpAddressFormGroup(): FormGroup {
    return this.incomingTruckFormGroup.get('driverScheduleAddressDTO') as FormGroup;
  }

  get paymentProcessingFormGroup(): FormGroup {
    return this.incomingTruckFormGroup.get('paymentProcessingDTO') as FormGroup;
  }

  get unitDTOFormGroup(): FormGroup {
    return this.incomingTruckFormGroup.get('unitDTO') as FormGroup;
  }

  get generalInformationFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('generalInformation') as FormGroup;
  }

  get internetOptionFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('internetOption') as FormGroup;
  }

  get unitLotLocationFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('unitLotLocation') as FormGroup;
  }

  get previousOwnerFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('previousOwner') as FormGroup;

  }
  get odometerFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('odometer') as FormGroup;
  }

  get createdByFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('createdBy') as FormGroup;
  }

  get newPaymentProcessingFormGroup(): FormGroup {
    return this.fb.group({
      transDate: new FormControl(null, [Validators.required]),
      stockNumber: new FormControl(null, [Validators.required]),
      assigneeId: new FormControl(null, [Validators.required]),
      enable: new FormControl(false),
      sendFundingEmail: new FormControl(false),
    })
  }
  get newAddressFormGroupPickup(): FormGroup {
    return this.fb.group({
      id: new FormControl(null),
      streetAddress: new FormControl(null, [Validators.required]),
      city: new FormControl(null, [Validators.required]),
      state: new FormControl(null, [Validators.required]),
      zipcode: new FormControl(null, [Validators.required]),
      location: new FormControl('PICK_UP'),
      latitude: new FormControl(null),
      longitude: new FormControl(null)
    })
  }

  get unitDtoFormGroup(): FormGroup {
    return this.fb.group({
      deleted: new FormControl(false),
      archived: new FormControl(false),
      id: new FormControl(),
      generalInformation: this.generalInformation,
      internetGroups: new FormControl([]),
      internetOption: this.internetOption,
      unitLotLocation: this.unitLotLocation,
      previousOwner: !this.isEditMode ? {} : this.previousOwner,
      odometer: this.odometer,
      createdBy: this.isEditMode ? this.createdBy : '',
      createdDate: new Date(),
      unitAssociations: new FormControl(null),
      unitAssociation: new FormControl(null),
    });
  }

  get generalInformation(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      year: new FormControl(new Date().getFullYear(), [Validators.required]),
      vin: new FormControl(null, [Validators.required]),
      designationId: new FormControl(null, [Validators.required]),
      makeId: new FormControl(null, [Validators.required]),
      unitModelId: new FormControl(null, [Validators.required]),
      ownerId: new FormControl(null, [Validators.required]),
      unitTypeId: new FormControl(null, [Validators.required]),
      unitTypeCategoryId: new FormControl(null, [Validators.required]),
    })
  }

  get internetOption(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      displayOnWeb: new FormControl(true),
      priceOnWeb: new FormControl(true),
      unitId: new FormControl()
    })
  }

  get unitLotLocation(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      currentLocationId: new FormControl(),
      receivingLocationId: new FormControl(),
      unitId: new FormControl()
    })
  }

  get odometer(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      odometerReading: new FormControl(),
      hours: new FormControl(),
      unitOfDistance: { id: 1 },
      unitId: new FormControl()
    })
  }

  get createdBy(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      name: new FormControl(),
    })
  }

  get previousOwner(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      previousOwnerName: new FormControl(),
      previousOwnerContactId: new FormControl(),
      unitId: new FormControl()
    })
  }

  get expensesFormGroup(): FormGroup {
    return this.incomingTruckFormGroup.get('incomingTruckExpenseDTO') as FormGroup;
  }

  get newExpensesFormGroup(): FormGroup {
    return this.fb.group({
      id: new FormControl(),
      poRoNumber: new FormControl(null),
      invoiceNumber: new FormControl(null),
      description: new FormControl(null),
      amount: new FormControl(null, [Validators.required]),
      acquisitionMethodId: new FormControl(null, [Validators.required]),
      expensesId: new FormControl(),
      expensesAttachmentDTO: this.expensesAttachments,
      purchasedById: new FormControl(null, [Validators.required]),
      assigneeId: new FormControl(null, [Validators.required]),
      vendorId: new FormControl(null, [Validators.required]),
      contactAndVendorAndSupplierType: new FormControl(null, [Validators.required]),
      crmContactId: new FormControl(null),
      supplierId: new FormControl(null),
      enable: new FormControl(false),
    });
  }

  private getUnitTypes(categoryId: number | string) {
    this.loaders.unitTypes = true;
    const endpoint = `${API_URL_UTIL.inventory.unitTypesList}?categoryId=${categoryId}`;
    this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (unitTypes) => {
        this.unitTypes = unitTypes;
        this.loaders.unitTypes = false;
      },
      error: () => {
        this.loaders.unitTypes = false;
      }
    });
  }

  private getMakeList(categoryId: number | string): void {
    this.loaders.makes = true;
    this.inventoryService.getCategoryByMakeList(categoryId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (makeListing) => {
        this.makes = makeListing;
        this.loaders.makes = false;
      },
      error: () => {
        this.loaders.makes = false;
      }
    });
  }

  private getModelList(makeId: number | string): void {
    this.loaders.models = true;
    const endpoint = API_URL_UTIL.inventory.makeBYModelList.replace(':makeId', makeId.toString())
    this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (models) => {
        this.models = models;
        this.loaders.models = false;
      },
      error: () => {
        this.loaders.models = false;
      }
    })
  }

  private getDesignationList(): void {
    this.loaders.designations = true;
    this.commonService.getList(API_URL_UTIL.designations.root).pipe(takeUntil(this.destroy$)).subscribe({
      next: (designations) => {
        this.designations = designations;
        this.loaders.designations = false;
      },
      error: () => {
        this.loaders.designations = false;
      }
    });
  }

  private getVendorList(): void {
    this.loaders.vendors = true;
    this.commonService.getList(API_URL_UTIL.vendorsContactsSuppliers).pipe(takeUntil(this.destroy$)).subscribe({
      next: (vendors) => {
        vendors.map((d: any) => {
          d.name = `${d.name} (${d.type})`;
          d.id = `${d.id}-${d.type}`;
          return d
        })
        this.vendors = vendors;
        this.loaders.vendors = false;
      },
      error: () => {
        this.loaders.vendors = false;
      }
    });
  }
  private getDealersList(): void {
    this.loaders.ownedBy = true;
    this.loaders.lotLocation = true;
    this.loaders.currentLocation = true;
    this.dealerService.getDealerList<IdNameModel>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (dealerOptions) => {
        this.dealerOptions = dealerOptions;
        this.loaders.ownedBy = false;
        this.loaders.lotLocation = false;
        this.loaders.currentLocation = false;
      },
      error: () => {
        this.loaders.ownedBy = false;
        this.loaders.lotLocation = false;
        this.loaders.currentLocation = false;
      }
    });
  }
  private getAcquisitionList(): void {
    this.loaders.acquisitionType = true;
    this.expensesService.getAcquisitionList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (acquisitionType) => {
        this.acquisitionType = acquisitionType;
        this.loaders.acquisitionType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.acquisitionType = false;
      }
    });
  }

  onCancel(): void {
    this.onClose.emit();
  }

  onSubmitAndAddNew(event: Event): void {
    this.onSubmit(false, event);
  }

  onStatusChange() {
    this.isStatusError = !(!!this.status);
  }

  get sendEmail() {
    return this.paymentProcessingFormGroup?.value?.sendFundingEmail;
  }

  editIncomingTruckData(): void {
    this.saveLoader = true;
    this.incomingTruckService.update(this.incomingTruckCreateParams).pipe(takeUntil(this.destroy$)).subscribe(
      {
        next: (res) => {
          this.toasterService.success(this.isEditMode ? MESSAGES.incomingTruckDataUpdateSuccess : MESSAGES.incomingTruckDataAddSuccess);
          if (this.sendEmail) {
            this.sendFundingEmail(res);
          } else {
            this.hasBeenModified.emit(true);
            this.incomingTruckFormGroup.reset();
            this.handleClose();
          }
        }, error: () => {
          this.handleSaveError();
        }
      }
    );
  }

  downloadImage(taskAttachment: ExpensesAttachment): void {
    if (taskAttachment?.url) {
      this.fileUploadService.downloadFile(taskAttachment.url, this.getFileName(taskAttachment.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(taskAttachment.url));
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  deleteImageFromCloud(imageUrl: string, fileIndex: number, callbackFn: Function): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      callbackFn(fileIndex);
    })
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');
  }

  setUnitDtoFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit) {
      return this.unitDTOFormGroup.patchValue({
        id: inventoryDetails?.unitId,
        deleted: inventoryDetails?.unit?.deleted,
        archived: inventoryDetails?.unit?.archived,
        createdDate: inventoryDetails?.unit?.createdDate,
        unitAssociations: inventoryDetails?.unit?.unitAssociations,
        unitAssociation: inventoryDetails?.unit?.unitAssociation
      })
    }
  }

  setInternetOptionFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit?.internetOption) {
      return this.internetOptionFormGroup.patchValue({
        id: inventoryDetails.unit.internetOption.id,
        displayOnWeb: inventoryDetails.unit.internetOption.displayOnWeb,
        priceOnWeb: inventoryDetails.unit.internetOption.priceOnWeb,
        unitId: inventoryDetails.unit.internetOption.unitId
      })
    }
  }

  setUnitLocationFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit?.unitLotLocation) {
      return this.unitLotLocationFormGroup.patchValue({
        id: inventoryDetails.unit.unitLotLocation.id,
        receivingLocationId: inventoryDetails.unit.unitLotLocation.receivingLocation.id,
        currentLocationId: inventoryDetails.unit.unitLotLocation.currentLocation.id,
        unitId: inventoryDetails.unit.unitLotLocation.unitId,
      })
    }
  }

  setPreviousOwnerFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit?.previousOwner) {
      return this.previousOwnerFormGroup.patchValue({
        id: inventoryDetails.unit.previousOwner.id,
        previousOwnerName: inventoryDetails.unit.previousOwner.previousOwnerName,
        previousOwnerContactId: inventoryDetails.unit.previousOwner.previousOwnerContactId,
        previousOwnerVendorId: inventoryDetails.unit.previousOwner.previousOwnerVendorId,
        previousOwnerSupplierId: inventoryDetails.unit.previousOwner.previousOwnerSupplierId,
        type: inventoryDetails.unit.previousOwner.type,
        unitId: inventoryDetails.unit.previousOwner.unitId
      })
    }
  }

  setOdometerFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit?.odometer) {
      return this.odometerFormGroup.patchValue({
        id: inventoryDetails?.unit?.odometer?.id,
        odometerReading: inventoryDetails?.unit?.odometer?.odometerReading,
        hours: inventoryDetails?.unit?.odometer?.hours,
        unitOfDistance: inventoryDetails?.unit?.odometer?.unitOfDistance,
        unitId: inventoryDetails?.unit?.odometer?.unitId,
      })
    }
  }

  setCreatedByFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit?.createdBy) {
      return this.createdByFormGroup.patchValue({
        id: inventoryDetails.unit.createdBy.id,
        name: inventoryDetails.unit.createdBy.name
      })
    }
  }

  setGeneralInformationFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.unit?.generalInformation) {
      if (inventoryDetails?.unit?.generalInformation?.unitTypeCategory?.id) {
        this.getUnitTypes(inventoryDetails.unit.generalInformation.unitTypeCategory.id)
        this.getMakeList(inventoryDetails.unit.generalInformation.unitTypeCategory.id)
      }
      if (inventoryDetails.unit.generalInformation?.make.id) {
        this.getModelList(inventoryDetails.unit.generalInformation?.make.id)
      }
      return this.generalInformationFormGroup.patchValue({
        id: inventoryDetails?.unit?.generalInformation?.id,
        vin: inventoryDetails?.unit.generalInformation?.vin,
        unitTypeId: inventoryDetails?.unit?.generalInformation?.unitType?.id,
        year: inventoryDetails?.unit?.generalInformation?.year,
        makeId: inventoryDetails?.unit?.generalInformation?.make?.id,
        unitModelId: inventoryDetails?.unit?.generalInformation?.unitModel?.id,
        ownerId: inventoryDetails?.unit?.generalInformation?.owner?.id,
        designationId: inventoryDetails?.unit?.generalInformation?.designation?.id,
        unitTypeCategoryId: inventoryDetails?.unit?.generalInformation?.unitTypeCategory?.id,
      })
    }
  }

  setPickUpAddressFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.driverScheduleAddress) {
      return this.pickUpAddressFormGroup.patchValue({
        id: inventoryDetails?.driverScheduleAddress?.id,
        streetAddress: inventoryDetails?.driverScheduleAddress?.streetAddress,
        city: inventoryDetails?.driverScheduleAddress?.city,
        state: inventoryDetails?.driverScheduleAddress?.state,
        zipcode: inventoryDetails?.driverScheduleAddress?.zipcode,
        location: inventoryDetails?.driverScheduleAddress?.location
      })
    }
  }

  setExpensesFormGroup(inventoryDetails: IncomingTruckDetails) {
    if (inventoryDetails?.incomingTruckExpenseDTO) {
      this.expensesFormGroup.patchValue({
        id: inventoryDetails?.incomingTruckExpenseDTO?.id,
        firstExpense: inventoryDetails?.incomingTruckExpenseDTO?.firstExpense,
        poRoNumber: inventoryDetails?.incomingTruckExpenseDTO?.poRoNumber,
        invoiceNumber: inventoryDetails?.incomingTruckExpenseDTO?.invoiceNumber,
        description: inventoryDetails?.incomingTruckExpenseDTO?.description,
        amount: inventoryDetails?.incomingTruckExpenseDTO?.amount,
        acquisitionMethodId: inventoryDetails?.incomingTruckExpenseDTO?.acquisitionMethod?.id,
        expensesAttachmentDTO: inventoryDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO,
        purchasedById: inventoryDetails?.incomingTruckExpenseDTO?.purchasingAgent?.id,
        contactAndVendorAndSupplierType: inventoryDetails?.incomingTruckExpenseDTO?.contactAndVendorAndSupplierType,
        vendorId: this.setVendorId,
        crmContactId: inventoryDetails?.incomingTruckExpenseDTO?.crmContact?.id,
        expenseId: inventoryDetails?.incomingTruckExpenseDTO?.expensesId,
        enable: inventoryDetails?.incomingTruckExpenseDTO?.enable,
        assigneeId: inventoryDetails?.incomingTruckExpenseDTO?.assignee?.id
      })
    }
  }

  get setVendorId(): string {
    switch (this.incomingTruckDetails?.incomingTruckExpenseDTO?.contactAndVendorAndSupplierType) {
      case ModelType.VENDOR.toUpperCase():
        return this.incomingTruckDetails?.incomingTruckExpenseDTO?.vendor?.id?.toString().concat(`-${ModelType.VENDOR.toUpperCase()}`) ?? '';
      case ModelType.CONTACT.toUpperCase():
        return this.incomingTruckDetails?.incomingTruckExpenseDTO?.crmContact?.id?.toString().concat(`-${ModelType.CONTACT.toUpperCase()}`) ?? '';
      case ModelType.SUPPLIER.toUpperCase():
        return this.incomingTruckDetails?.incomingTruckExpenseDTO?.supplier?.id?.toString().concat(`-${ModelType.SUPPLIER.toUpperCase()}`) ?? '';
      default:
        return "";
    }
  }

  setPaymentFormGroup(inventoryDetails: IncomingTruckDetails) {
    return this.paymentProcessingFormGroup.patchValue({
      stockNumber: inventoryDetails?.unit?.generalInformation?.stockNumber,
      enable: inventoryDetails?.unit?.generalInformation?.enable,
      assigneeId: inventoryDetails?.unit?.generalInformation?.assignee?.id,
      transDate: inventoryDetails?.incomingTruckExpenseDTO?.transDate ? new Date(`${this.incomingTruckDetails?.incomingTruckExpenseDTO?.transDate}`) : '',
    })
  }

  setIncomingTruckInfoFormGroup() {
    if (this.incomingTruckDetails) {
      this.setUnitDtoFormGroup(this.incomingTruckDetails);
      this.setInternetOptionFormGroup(this.incomingTruckDetails);
      this.setUnitLocationFormGroup(this.incomingTruckDetails);
      this.setPreviousOwnerFormGroup(this.incomingTruckDetails);
      this.setOdometerFormGroup(this.incomingTruckDetails);
      this.setCreatedByFormGroup(this.incomingTruckDetails);
      this.setGeneralInformationFormGroup(this.incomingTruckDetails);
      this.setPickUpAddressFormGroup(this.incomingTruckDetails);
      this.setExpensesFormGroup(this.incomingTruckDetails);
      this.setPaymentFormGroup(this.incomingTruckDetails);
      this.status = this.incomingTruckDetails.status;
      this.pickupNotes = this.incomingTruckDetails.pickupNotes;
    }
    if (this.isViewMode) {
      this.incomingTruckFormGroup.disable();
    }
    if (this.incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO?.url) {
      this.isUploadFlag = false;
    }
  }

  toggleGoogleMapPopUp() {
    this.showGoogleMapSideBar = !this.showGoogleMapSideBar;
    this.getFullAddress()
  }

  handleAddressChange(address: Address | any) {
    if (address?.address_components) {
      this.pickUpAddressFormGroup.controls['streetAddress'].setValue(address.name);
      this.pickUpAddressFormGroup.controls['latitude'].setValue(address.geometry.location.lat());
      this.pickUpAddressFormGroup.controls['longitude'].setValue(address.geometry.location.lng());
      address.address_components.forEach((addressComponent: any) => {
        switch (addressComponent.types[0]) {
          case 'sublocality':

            break;
          case 'locality':
            this.pickUpAddressFormGroup.controls['city'].setValue(addressComponent.long_name);
            break;
          case 'administrative_area_level_1':
            this.pickUpAddressFormGroup.controls['state'].setValue(addressComponent.long_name);
            break;
          case 'postal_code':
            this.pickUpAddressFormGroup.controls['zipcode'].setValue(addressComponent.long_name);
            break;
        }
      });
    }
  }

  hideShowAttachmentsTab() {
    this.showAttachmentsTab = !this.incomingTruckInfo?.incomingTruckExpenseDTO?.expensesAttachmentDTO?.url ? false : true;
  }

  getFullAddress() {
    const rowAddress = []
    const address = this.pickUpAddressFormGroup.value;
    rowAddress.push(address?.streetAddress ? address.streetAddress : '')
    rowAddress.push(address?.city ? address.city : '')
    rowAddress.push(address?.state ? address.state : '')
    rowAddress.push(address?.zipcode ? address.zipcode : '')
    const cleanAddress = rowAddress.filter(str => {
      return str !== ""
    })
    this.fullAddress = cleanAddress.join(", ")
  }
  private getCategoryTypes() {
    this.loaders.categoryType = true;
    this.inventoryService.getCategoryType().pipe(takeUntil(this.destroy$)).subscribe({
      next: (categoryTypes: any) => {
        this.categoryTypes = categoryTypes;
        if (this.categoriesToDisplay?.length) {
          this.categoryTypes = this.categoriesToDisplay;
        }
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.loaders.categoryType = false;
        this.cdf.detectChanges();
      }
    });
  }

  changeCategory(id: number) {
    this.categoryId = id;
    this.getUnitTypes(id);
    this.getMakeList(id)
  }

  changeMake(id: number) {
    this.selectedMakeId = id;
    this.getModelList(id);
  }

  private getPurchaseByName(): void {
    this.loaders.purchaseBy = true;
    this.soldTruckService.getPipelineOwnerList().pipe(takeUntil(this.destroy$)).subscribe({
      next: (purchaseByList) => {
        this.purchaseByList = purchaseByList;
        this.loaders.purchaseBy = false;
      },
      error: () => {
        this.loaders.purchaseBy = false;
      }
    });
  }

  onAddEditIncomingTruckPopupClose(refreshList: boolean): void {
    this.showIncomingCreateModal = false;
    this.categoriesToShow = []
  }

  onAcquisitionCheckedChange(checked: boolean) {
    this.enablePaymentTab = checked;
  }

  onGeneralCheckedChange(checked: boolean) {
    this.enablePickupTab = checked;
  }

  statusChanged(status: string) {
    this.status = status;
  }

  handelPickupNotesChanged(note: string): void {
    this.pickupNotes = note;
  }

  onGetVendorList() {
    this.getVendorList();
  }

  onGetUnitTypes(categoryId: number) {
    this.getUnitTypes(categoryId);
  }

  onGetMakeList(categoryId: number) {
    this.getMakeList(categoryId);
  }

  onGetModelList(selectedMakeId: number) {
    this.getModelList(selectedMakeId);
  }
  changeUploadFlag(bool: boolean) {
    this.isUploadFlag = bool;
    if (bool) {
      this.expensesFormGroup.value.expensesAttachmentDTO = null;
    }
  }

  get incomingTruckCreateParams(): IncomingTruckCreateParams {
    const params: IncomingTruckCreateParams = {};
    const vendorId = Number(this.expensesFormGroup.controls['vendorId'].value.split('-')[0]);
    const contactAndVendorAndSupplierType = this.expensesFormGroup.controls['contactAndVendorAndSupplierType'].value;
    const transDate = this.paymentProcessingFormGroup.value.transDate;

    const createExpenseDTO = () => {
      const commonValues = {
        ...this.incomingTruckFormGroup.controls.incomingTruckExpenseDTO.value,
        transDate,
      };
      if (contactAndVendorAndSupplierType === ModelType.CONTACT.toUpperCase()) {
        return { ...commonValues, vendorId: null, supplierId: null, crmContactId: vendorId }
      } else if (contactAndVendorAndSupplierType === ModelType.VENDOR.toUpperCase()) {
        return { ...commonValues, vendorId, crmContactId: null, supplierId: null }
      } else {
        return { ...commonValues, vendorId: null, crmContactId: null, supplierId: vendorId }
      }
    };
    const setPreviousOwner = (): PreviousOwner => {
      const previousOwner = new PreviousOwner();
      previousOwner.type = contactAndVendorAndSupplierType;
      if (contactAndVendorAndSupplierType === ModelType.CONTACT.toUpperCase()) {
        previousOwner.previousOwnerContactId = vendorId;
      } else if (contactAndVendorAndSupplierType === ModelType.VENDOR.toUpperCase()) {
        previousOwner.previousOwnerVendorId = vendorId;
      } else {
        previousOwner.previousOwnerSupplierId = vendorId;
      }
      return previousOwner;
    }
    params.incomingTruckExpenseDTO = createExpenseDTO();
    params.unitDTO = {
      ...this.incomingTruckFormGroup.controls.unitDTO.value,
      id: this.incomingTruckDetails?.unit?.id
    };
    if (params.unitDTO) {
      params.unitDTO.generalInformation.assigneeId = this.paymentProcessingFormGroup.value.assigneeId;
      params.unitDTO.generalInformation.enable = this.paymentProcessingFormGroup.value.enable;
      params.unitDTO.generalInformation.stockNumber = this.paymentProcessingFormGroup.value.stockNumber;
      params.unitDTO.previousOwner = { ...params.unitDTO.previousOwner, ...setPreviousOwner() };
    }
    if (this.enablePaymentTab) {
      params.listingStatus = 'AVAILABLE';
    }
    if (this.enablePickupTab) {
      params.driverScheduleAddressDTO = this.incomingTruckFormGroup.controls.driverScheduleAddressDTO.value;
      params.pickupNotes = this.pickupNotes;
    }
    if (this.isEditMode && params?.incomingTruckExpenseDTO && this.incomingTruckDetails && this.incomingTruckDetails?.incomingTruckExpenseDTO?.expensesId) {
      params.incomingTruckExpenseDTO.expenseId = this.incomingTruckDetails.incomingTruckExpenseDTO.expensesId;
    }
    params.sentEmail = this.sendEmail || this.incomingTruckDetails?.sentEmail || false;
    return {
      ...params,
      id: this.isEditMode ? this.incomingTruckDetails?.id : undefined,
      unitId: this.isEditMode ? this.incomingTruckDetails?.unitId : undefined,
      status: this.status
    };
  }

  saveIncomingTruckData(close: boolean): void {
    if (close) {
      this.saveLoader = true;
    } else {
      this.saveAndAddLoader = true;
    }
    this.incomingTruckService
      .add<IncomingTruckCreateParams>(this.incomingTruckCreateParams)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: IncomingTruckDetails) => {
          if (this.sendEmail) {
            this.sendFundingEmail(res, close);
          }
          else {
            this.handleSaveSuccess(res, close);
          }
        },
        error: () => {
          this.handleSaveError();
        }
      });
  }

  private sendFundingEmail(res: IncomingTruckDetails, close = true): void {
    this.incomingTruckService.get(res.id, API_URL_UTIL.incomingTruck.fundingEmail).pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(MESSAGES.incomingTruckFundingEmail);
          if (this.isEditMode) {
            this.hasBeenModified.emit(true);
            this.incomingTruckFormGroup.reset();
            this.handleClose();
          } else {
            this.handleSaveSuccess(res, close);
          }
        }
      })
  }

  private handleSaveSuccess(res: IncomingTruckDetails, close: boolean): void {
    this.toasterService.success(MESSAGES.incomingTruckDataAddSuccess);
    this.hasBeenModified.emit(true);
    if (close) {
      this.incomingTruckDetails = res;
    }
    setTimeout(() => {
      if (close) {
        this.handleClose();
      } else {
        this.handleSaveAndAdd();
      }
    }, 1000);
  }

  private handleClose(): void {
    this.onClose.emit();
    this.commonSharedService.setBlockUI$(false);
    this.saveLoader = false;
    this.cdf.detectChanges();
  }

  private handleSaveAndAdd(): void {
    this.incomingTruckFormGroup.reset(this.initialFormValues);
    this.fileUploadProgresses = [];
    this.acquisitionInfoComponent.isUploadFlag = true;
    this.pickupNotes = '';
    this.hasBeenModified.emit(true);
    this.commonSharedService.setBlockUI$(false);
    this.saveAndAddLoader = false;
    this.cdf.detectChanges();
  }

  private handleSaveError(): void {
    this.saveLoader = false;
    this.saveAndAddLoader = false;
    this.commonSharedService.setBlockUI$(false);
    this.cdf.detectChanges();
  }


  displayEmailError() {
    const errorMessage = this.enablePaymentTab ? `Document in ${this.ACQUISITION} tab` : `${this.PAYMENT} tab information`;
    this.message = IncomingTruckTabError.emailWarning.replace('{data}', errorMessage);
    this.displayBasic = true;
  }

  checkAllTabs(): void {
    const message: string[] = [];
    const checkAcquisition = () => {
      if (this.expensesFormGroup.invalid || this.generalInformationFormGroup.invalid) {
        message.push(this.ACQUISITION);
      }
    };
    const checkPayment = () => {
      if (this.paymentProcessingFormGroup.invalid) {
        message.push(this.PAYMENT);
      }
    };
    const checkPickup = () => {
      if (this.pickUpAddressFormGroup.invalid) {
        message.push(this.PICKUP);
      }
    };
    switch (this.activeIndex) {
      case 0:
        checkPayment();
        checkPickup();
        break;
      case 1:
        checkAcquisition();
        checkPickup();
        break;
      case 2:
        checkAcquisition();
        checkPayment();
        break;
      default:
        break;
    }
    if (message.length) {
      if (message.length > 1) {
        message.splice(1, 0, "and");
      }
      this.displayBasic = true;
      this.message = IncomingTruckTabError.warning.replace('{tab}', message.join(" "));
    }
  }

  hideWarning() {
    this.displayBasic = false;
    this.message = "";
  }

  get canSendEmail(): boolean {
    return (this.enablePaymentTab && !this.isUploadFlag);
  }

  onSubmit(close = false, event: Event): void {
    if (this.isViewMode) {
      this.incomingTruckFormGroup.enable();
      this.isViewMode = false;
      this.isEditMode = true;
      this.isEditTitle.emit(true);
      return;
    }
    this.isStatusError = !(!!this.status);
    this.incomingTruckFormGroup.updateValueAndValidity();
    if (!this.enablePaymentTab && (this.expensesFormGroup.invalid || this.generalInformationFormGroup.invalid)) {
      this.expensesFormGroup.markAllAsTouched();
      this.generalInformationFormGroup.markAllAsTouched();
      return;
    }
    if (this.enablePaymentTab && !this.enablePickupTab && (this.expensesFormGroup.invalid || this.unitDTOFormGroup.invalid || this.paymentProcessingFormGroup.invalid)) {
      this.expensesFormGroup.markAllAsTouched();
      this.generalInformationFormGroup.markAllAsTouched();
      this.paymentProcessingFormGroup.markAllAsTouched();
      if (this.activeIndex === 0 && this.paymentProcessingFormGroup.invalid) {
        this.message = IncomingTruckTabError.warning.replace('{tab}', this.PAYMENT);
      }
      else if (this.activeIndex === 1 && this.expensesFormGroup.invalid || this.unitDTOFormGroup.invalid) {
        this.message = IncomingTruckTabError.warning.replace('{tab}', this.ACQUISITION);
      }
      if (this.message) {
        this.displayBasic = true;
      }
      return;
    }
    else if (this.enablePaymentTab && this.enablePickupTab && this.incomingTruckFormGroup.invalid) {
      this.incomingTruckFormGroup.markAllAsTouched();
      this.checkAllTabs();
      return;
    }
    if (this.sendEmail) {
      if (!this.canSendEmail) {
        this.displayEmailError();
        return;
      }
    }
    this.checkUnitStatus(close, event)
  }

  checkUnitStatus(close: boolean, event: Event): void {
    if (this.status === IncomingTruckStatusList[2].value) {
      this.cdf.detectChanges();
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        header: 'Confirmation',
        message: MESSAGES.incomingTruckStatusArrived,
        icon: icons.triangle,
        accept: () => {
          this.saveData(close);
        },
        reject: () => {
          return;
        }
      });
    } else {
      this.saveData(close);
    }
  }

  saveData(close: boolean) {
    this.commonSharedService.setBlockUI$(true);
    if (this.isEditMode) {
      this.editIncomingTruckData();
    } else {
      this.saveIncomingTruckData(close)
    }
  }
}
