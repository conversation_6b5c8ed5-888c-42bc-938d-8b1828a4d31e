import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormGroupDirective } from '@angular/forms';
import { Constants, MESSAGES, icons } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { Account } from '@pages/auth/models';
import { ExpensesService } from '@pages/inventory/services/expenses.service';
import { InventoryService } from '@pages/inventory/services/inventory.service';
import { ExpensesAttachment, IncomingTruckDetails, ModelType } from '@pages/transport/models/incoming-truck.model';
import { User } from '@pages/user/models';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress, IdNameModel } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-acquisition-info',
  templateUrl: './acquisition-info.component.html',
  styleUrls: ['./acquisition-info.component.scss']
})
export class AcquisitionInfoComponent extends BaseComponent implements OnInit {
  @Input() incomingTruckDetails!: IncomingTruckDetails | null;
  @Input() fileUploadProgresses: FileUploadProgress[] = [];
  @Input() isEditMode !: boolean;
  @Input() users!: User[];
  @Input() isStatusError !: boolean;
  @Input() isViewMode !: boolean | null;
  @Input() acquisitionType: IdNameModel[] = [];
  @Input() vendors: IdNameModel[] = [];
  @Input() purchaseByList: IdNameModel[] = [];
  @Input() displaySelectionDialog !: boolean;
  @Input() showAttachmentsTab !: boolean;
  @Input() isUploadFlag!: boolean;
  @Input() incomingTruckExpenseForm!: string;
  @Input() unitDTOForm!: string;
  @Input() categoryId!: number;
  @Input() currentUser!: Account | null;
  @Input() unitTypes !: IdNameModel[];
  @Input() makes !: IdNameModel[];
  @Input() models !: IdNameModel[];
  @Input() categoryTypes: IdNameModel[] = [];
  @Input() dealerOptions !: IdNameModel[];
  @Input() designations !: IdNameModel[];
  @Output() checkedChange: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() getVendorListEvent = new EventEmitter<void>();
  @Output() statusChanged = new EventEmitter<string>();
  @Output() changeParentCategory = new EventEmitter<number>();
  @Output() getUnitTypesEvent = new EventEmitter<number>();
  @Output() getMakeListEvent = new EventEmitter<number>();
  @Output() getModelListEvent = new EventEmitter<number>();
  @Output() changeUploadFlag = new EventEmitter<boolean>();
  taskFileUploadPath = 'Expenses Files';
  ModelType = ModelType;
  incomingTruckExpense!: FormGroup;
  selectedMakeId!: number;
  generalInfoFormGroup!: FormGroup;
  unitDTOFormGroup!: FormGroup;
  acquisitionFile!: File;
  enableGeneralTab = false;
  modelPopups = {
    showCreateVendor: false,
    showCreateSupplier: false,
    showCreateContact: false,
    showCreateModel: false,
    showCreateMake: false,
    showCreateUnitType: false
  };
  @Input() loaders = {
    makes: false,
    models: false,
    categories: false,
    years: false,
    status: false,
    unitTypes: false,
    designations: false,
    vendors: false,
    stockNumbers: false,
    acquisitionType: false,
    lotLocation: false,
    currentLocation: false,
    ownedBy: false,
    categoryType: false,
    purchaseBy: false,
    previousOwnerName: false,
    users: false
  };

  constructor(
    private readonly rootFormGroup: FormGroupDirective,
    private readonly toasterService: AppToasterService,
    private readonly inventoryService: InventoryService,
    private readonly fileUploadService: FileUploadService,
    private readonly cdf: ChangeDetectorRef,
    private readonly expensesService: ExpensesService,
    private readonly confirmationService: ConfirmationService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.incomingTruckExpense = this.rootFormGroup.control.get(this.incomingTruckExpenseForm) as FormGroup;
    this.unitDTOFormGroup = this.rootFormGroup.control.get(this.unitDTOForm) as FormGroup;
    this.generalInfoFormGroup = this.unitDTOFormGroup.controls['generalInformation'] as FormGroup;
  }

  setContactVendorSupplier(event: any): void {
    if (event) {
      const selectedContact: IdNameModel = this.vendors.find(vendor => vendor.id === event.value);
      if (selectedContact && selectedContact.archived) {
        this.incomingTruckExpense.controls['vendorId'].setValue(null);
        this.toasterService.warning(MESSAGES.archivedContact);
      return;
      }
    }
    this.cdf.detectChanges(); 
    const selectedText = event.originalEvent.srcElement.innerText;
    const type = selectedText && selectedText.split('(')[1].split(')')[0].trim();

    switch (type) {
      case ModelType.CONTACT.toUpperCase():
        this.incomingTruckExpense.controls['contactAndVendorAndSupplierType'].setValue(ModelType.CONTACT.toUpperCase());
        break;
      case ModelType.VENDOR.toUpperCase():
        this.incomingTruckExpense.controls['contactAndVendorAndSupplierType'].setValue(ModelType.VENDOR.toUpperCase());
        break;
      case ModelType.SUPPLIER.toUpperCase():
        this.incomingTruckExpense.controls['contactAndVendorAndSupplierType'].setValue(ModelType.SUPPLIER.toUpperCase())
        break;
      default:
        break;
    }
  }

  onFileSelect(event: any): void {
    // TODO Will be remove once complete
    // if (event.target?.files?.length) {
    //   this.changeUploadFlag.emit(false);
    //   for (const file of event.target.files) {
    //     const extensionDot = file?.name?.lastIndexOf('.');
    //     const ext = file?.name?.substring(extensionDot + 1);
    //     if (!Constants.allowedPdfFormats.includes(ext)) {
    //       this.toasterService.error(MESSAGES.fileTypeNotSupported);
    //       return;
    //     }
    //     this.uploadImage(file);
    //   }
    // }
    for (const file of event.target.files) {
      if (file.size > this.constants.fileSize) {
        this.toasterService.warning(MESSAGES.fileUploadMessage)
      }
      else {
        if (event.target?.files?.length) {
          if (file.size > this.constants.fileSize) {
            this.toasterService.warning(MESSAGES.fileUploadMessage)
            return
          }
          const modifiedFileName = getRefactorFileName(file.name);
          const isExtensionSupported = allowExtensions(modifiedFileName,Constants.allowedImgAndOdfFormats)
          if (isExtensionSupported) {
            const modifiedFile = new File([file], modifiedFileName, { type: file.type });
            this.uploadImage(modifiedFile);
          } else {
            this.toasterService.error(MESSAGES.fileTypeSupportedImgAndPDF);
            return
          }
        }
      }
    }
  }

  uploadImage(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.acquisitionFile = file;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.photoUploadSuccess);
          this.incomingTruckExpense.patchValue({
            expensesAttachmentDTO: this.expensesAttachments
          })
          if (this.fileUploadProgresses.length === 1) {
            this.changeUploadFlag.emit(false);
            this.cdf.detectChanges();
          }
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
        }
      })
  }

  get expensesAttachments(): ExpensesAttachment {
    let expensesAttachments: ExpensesAttachment[] = this.fileUploadProgresses.map(fileProgress => ({ url: fileProgress.uploadedFileUrl, fileName: this.acquisitionFile.name }));
    if (this.isEditMode && this.incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO?.url) {
      expensesAttachments = [this.incomingTruckDetails.incomingTruckExpenseDTO.expensesAttachmentDTO, ...expensesAttachments];
    }
    return expensesAttachments[0];
  }

  downloadImage(taskAttachment?: ExpensesAttachment | null): void {
    if (taskAttachment?.url) {
      this.fileUploadService.downloadFile(taskAttachment.url, this.getFileName(taskAttachment.url)).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/image" }), this.getFileName(taskAttachment.url));
        }
      });
    }
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  changeMake(id: number): void {
    this.generalInfoFormGroup.patchValue({
      unitModelId: null,
    });
    this.selectedMakeId = id;
    this.getModelList(id);
  }

  private getModelList(makeId: number | string): void {
    this.loaders.models = true;
    const endpoint = API_URL_UTIL.inventory.makeBYModelList.replace(':makeId', makeId.toString())
    this.inventoryService.getList<IdNameModel>(endpoint).pipe(takeUntil(this.destroy$)).subscribe({
      next: (models) => {
        this.models = models;
        this.loaders.models = false;
      },
      error: () => {
        this.loaders.models = false;
      }
    })
  }

  changeCategory(id: number): void {
    this.generalInfoFormGroup.patchValue({
      unitModelId: null,
      unitTypeId: null,
      makeId: null
    });
    this.changeParentCategory.emit(id);
  }

  getFileName(fileUrl: string | undefined): string {
    if (!fileUrl) {
      return '';
    }
    return fileUrl.split('_').slice(1).join('_');
  }

  onDelete(attachmentId: number | undefined, event: Event): void {
    if (!attachmentId) {
      if (this.incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO) {
        this.incomingTruckDetails.incomingTruckExpenseDTO.expensesAttachmentDTO = null;
      }
      this.incomingTruckExpense.patchValue({
        expensesAttachmentDTO: null
      })
      this.changeUploadFlag.emit(true);
    } else {
      this.confirmationService.confirm({
        target: event?.target as EventTarget,
        header: 'Confirmation',
        message: MESSAGES.deleteWarning.replace('{record}', 'attachment'),
        icon: icons.triangle,
        accept: () => {
          this.onDeleteConfirmation(attachmentId);
        }
      });
    }
  }

  onDeleteConfirmation(attachmentId: number | null): void {
    if (attachmentId) {
      this.expensesService.deleteExpenseAttachment(attachmentId).pipe(takeUntil(this.destroy$)).subscribe(() => {
        this.toasterService.success(MESSAGES.attachmentDeleteSuccess);
        if (this.incomingTruckDetails) {
          this.incomingTruckDetails.incomingTruckExpenseDTO.expensesAttachmentDTO = null;
        }
        if (!this.incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO?.url) {
          this.changeUploadFlag.emit(true);
        }
        this.cdf.detectChanges();
      });
    } else {
      return;
    }
  }

  removeFileFromUpload(fileIndex: number): void {
    const fileProgress = this.fileUploadProgresses[fileIndex];
    if (fileProgress.uploadedFileUrl) {
      this.deleteImageFromCloud(fileProgress.uploadedFileUrl);
    }
  }

  deleteImageFromCloud(imageUrl: string): void {
    this.fileUploadService.deleteFile([imageUrl]).pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.fileUploadProgresses = [];
      if (this.incomingTruckDetails?.incomingTruckExpenseDTO?.expensesAttachmentDTO) {
        this.incomingTruckDetails.incomingTruckExpenseDTO.expensesAttachmentDTO = null;
      }
      this.incomingTruckExpense.patchValue({
        expensesAttachmentDTO: null
      })
      this.changeUploadFlag.emit(true);
      this.cdf.detectChanges();
    })
  }

  get unitLotLocationFormGroup(): FormGroup {
    return this.unitDTOFormGroup.get('unitLotLocation') as FormGroup;
  }

  setLotLocation(ownerId: number): void {
    this.unitLotLocationFormGroup.patchValue({
      receivingLocationId: ownerId,
      currentLocationId: ownerId,
    })
  }

  showDialog(): void {
    this.displaySelectionDialog = true;
  }

  onCheckedChangeAcquisition(): void {
    this.checkedChange.emit(this.enableGeneralTab);
  }

  onAddEditPopupClose(modelType: string): void {
    this.modelPopups.showCreateVendor = false;
    this.modelPopups.showCreateSupplier = false;
    this.modelPopups.showCreateContact = false;
    this.modelPopups.showCreateMake = false;
    this.modelPopups.showCreateModel = false;
    this.modelPopups.showCreateUnitType = false;
    this.displaySelectionDialog = false;
    switch (modelType) {
      case ModelType.VENDOR:
      case ModelType.CONTACT:
      case ModelType.SUPPLIER:
        this.getVendorListEvent.emit();
        break;
      case ModelType.UNIT_TYPE:
        this.getUnitTypesEvent.emit(this.categoryId);
        break;
      case ModelType.MAKE:
        this.getMakeListEvent.emit(this.categoryId);
        break;
      case ModelType.MODEL:
        this.getModelListEvent.emit(this.selectedMakeId);
        break;
      default:
        break;
    }
  }

  openModel(modelType: string): void {
    switch (modelType) {
      case ModelType.MAKE:
        if (this.generalInfoFormGroup.controls.unitTypeCategoryId?.value) {
          this.modelPopups.showCreateMake = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingMake);
        }
        break;
      case ModelType.UNIT_TYPE:
        if (this.generalInfoFormGroup.controls.unitTypeCategoryId?.value) {
          this.modelPopups.showCreateUnitType = true;
        } else {
          this.toasterService.warning(MESSAGES.addCategoryBeforeAddingUnitType);
        }
        break;
      case ModelType.MODEL:
        if (this.generalInfoFormGroup.controls.makeId?.value) {
          this.modelPopups.showCreateModel = true;
        } else {
          this.toasterService.warning(MESSAGES.addMakeBeforeAddingModel);
        }
        break;
      default:
        break;
    }
  }

  openAndCloseModel(type: string): void {
    switch (type) {
      case ModelType.VENDOR:
        this.modelPopups.showCreateVendor = true;
        break;
      case ModelType.SUPPLIER:
        this.modelPopups.showCreateSupplier = true;
        break;
      case ModelType.CONTACT:
        this.modelPopups.showCreateContact = true;
        break;
      default:
        break;
    }
  }

  assignToMe(): void {
    this.incomingTruckExpense.patchValue({
      assigneeId: this.currentUser?.id
    });
  }

  isOptionDisabled(option: { archived: boolean; id: number }): boolean {
    const selectedValue = this.incomingTruckExpense?.get('vendorId')?.value as number;
    return option.archived && option.id !== selectedValue;
  }
}
