import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { Constants, icons, MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { allowExtensions, getRefactorFileName } from '@core/utils/fileUpaloadName.util';
import { AuthService } from '@pages/auth/services/auth.service';
import { CrmContactDocumentsService } from '@pages/crm/services/crm-contact-documents.service';
import { CRMContactDocumentListItem } from '@pages/inventory/models/documents.model';
import * as saveAs from 'file-saver';
import { ConfirmationService } from 'primeng/api';
import { takeUntil } from 'rxjs';
import { FileProperties, FileUploadProgress } from 'src/app/@shared/models';
import { FileUploadService } from 'src/app/@shared/services/file-upload.service';

@Component({
  selector: 'app-crm-contact-documents',
  templateUrl: './crm-contact-documents.component.html',
  styleUrls: ['./crm-contact-documents.component.scss']
})
export class CrmContactDocumentsComponent extends BaseComponent implements OnInit {

  @Input() crmContactId!: string;

  fileUploadProgresses: FileUploadProgress[] = [];
  taskFileUploadPath = 'CRM Contact Document Files';
  documentList: CRMContactDocumentListItem[] = [];

  constructor(
    private readonly cdf: ChangeDetectorRef,
    private readonly fileUploadService: FileUploadService,
    private readonly toasterService: AppToasterService,
    private readonly authService: AuthService,
    private readonly crmContactDocumentsService: CrmContactDocumentsService,
    private readonly confirmationService: ConfirmationService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  getAll(): void {
    this.isLoading = true;
    this.crmContactDocumentsService.get<CRMContactDocumentListItem[]>(this.crmContactId, API_URL_UTIL.admin.crm.list)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CRMContactDocumentListItem[]) => {
          this.documentList = res.reverse();
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onFileSelect(event: any) {
    if (event.target?.files?.length) {
      for (const file of event.target.files) {
        const modifiedFileName = getRefactorFileName(file?.name);
        const isExtensionSupported = allowExtensions(modifiedFileName, Constants.allowedPdfFormats)
        if (isExtensionSupported) {
          const modifiedFile = new File([file], modifiedFileName, { type: file.type });
          this.uploadDocument(modifiedFile);
        } else {
          this.toasterService.error(MESSAGES.fileTypeNotSupported);
          return
        }
      }
    }
  }

  uploadDocument(file: File): void {
    const formData: FormData = new FormData();
    formData.append('multipartFile', file, `${file.name}`);
    const uploadProgress = new FileUploadProgress();
    uploadProgress.index = this.fileUploadProgresses.length;
    uploadProgress.file = file;
    this.fileUploadProgresses.push(uploadProgress);
    this.fileUploadService.setFileUploads(this.fileUploadProgresses);
    this.setFileUrl(file, uploadProgress);
    this.fileUploadService.uploadFile(formData, uploadProgress.index, `${this.currentUser?.id}`, this.taskFileUploadPath, this.fileUploadProgresses)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (fileUrl: { url: string }) => {
          uploadProgress.uploadedFileUrl = fileUrl.url;
          this.fileUploadService.setFileUploads(this.fileUploadProgresses);
          this.toasterService.success(MESSAGES.documentUploadSuccess);
          this.onSaveDocument(fileUrl.url, file.name);
          this.cdf.detectChanges();
        },
        error: err => {
          this.toasterService.error(MESSAGES.fileUploadError);
          this.cdf.detectChanges();
        }
      })
  }

  setFileUrl(file: File, uploadProgress: FileUploadProgress): void {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      uploadProgress.fileProperty = new FileProperties(file.name, reader.result as any);
    }
  }

  onSaveDocument(url: string, fileName: string): void {
    const params = [
      {
        "url": url,
        "crmContactId": Number(this.crmContactId),
        "fileName": fileName
      }
    ]
    this.crmContactDocumentsService.add(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CRMContactDocumentListItem[]) => {
          this.getAll();
          this.isLoading = false;
          this.cdf.detectChanges();
        },
        error: () => {
          this.isLoading = false;
          this.cdf.detectChanges();
        }
      });
  }

  onDownload(file: CRMContactDocumentListItem): void {
    if (file?.url) {
      this.fileUploadService.downloadFile(file.url, file.fileName).pipe(takeUntil(this.destroy$)).subscribe({
        next: (response: ArrayBuffer) => {
          saveAs(new Blob([response], { type: "application/pdf" }), file.fileName);
        }
      });
    }
  }

  onDelete(file: CRMContactDocumentListItem, event: any): void {
    this.confirmationService.confirm({
      target: event?.target as EventTarget,
      message: MESSAGES.deleteWarning.replace('{record}', 'document'),
      icon: icons.triangle,
      header: "Confirmation",
      accept: () => {
        this.onDeleteConfirmation(file);
      }
    });
  }

  onDeleteConfirmation(file: CRMContactDocumentListItem): void {
    if (file.id) {
      this.isLoading = true;
      this.crmContactDocumentsService.delete(file.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (res: CRMContactDocumentListItem[]) => {
            this.getAll();
            this.cdf.detectChanges();
          },
          error: () => {
            this.isLoading = false;
            this.cdf.detectChanges();
          }
        });
    }
  }

  private getCurrentUser(): void {
    this.authService.getCurrentUser().pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user) {
        this.currentUser = user;
      }
    });
  }
}
