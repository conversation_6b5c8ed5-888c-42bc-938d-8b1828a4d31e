import { Injectable } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FilterCategories, FilterMakes, FilterModels, FilterUnitTypes } from '../models';

export interface UrlFilterParams {
  category?: number;
  unitTypes?: number[];
  makes?: number[];
  models?: number[];
  fromYear?: string;
  toYear?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: number;
  page?: number;
}

export interface FilterState {
  selectedCategory: number | null;
  selectedUnitTypes: FilterUnitTypes[];
  selectedMakes: FilterMakes[];
  selectedModels: FilterModels[];
  selectedFromYear: string | null;
  selectedToYear: string | null;
  minPrice: number;
  maxPrice: number | null;
  selectedSortBy: number | null;
  currentPage: number;
}

@Injectable({
  providedIn: 'root'
})
export class UrlFilterService {

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {}

  /**
   * Read filter parameters from current URL
   */
  getFiltersFromUrl(): UrlFilterParams {
    const queryParams = this.activatedRoute.snapshot.queryParams;
    const filters: UrlFilterParams = {};

    // Parse category
    if (queryParams['category']) {
      const category = parseInt(queryParams['category'], 10);
      if (!isNaN(category)) {
        filters.category = category;
      }
    }

    // Parse unit types (comma-separated)
    if (queryParams['unitTypes']) {
      const unitTypes = queryParams['unitTypes'].split(',')
        .map((id: string) => parseInt(id.trim(), 10))
        .filter((id: number) => !isNaN(id));
      if (unitTypes.length > 0) {
        filters.unitTypes = unitTypes;
      }
    }

    // Parse makes (comma-separated)
    if (queryParams['makes']) {
      const makes = queryParams['makes'].split(',')
        .map((id: string) => parseInt(id.trim(), 10))
        .filter((id: number) => !isNaN(id));
      if (makes.length > 0) {
        filters.makes = makes;
      }
    }

    // Parse models (comma-separated)
    if (queryParams['models']) {
      const models = queryParams['models'].split(',')
        .map((id: string) => parseInt(id.trim(), 10))
        .filter((id: number) => !isNaN(id));
      if (models.length > 0) {
        filters.models = models;
      }
    }

    // Parse year range
    if (queryParams['fromYear']) {
      filters.fromYear = queryParams['fromYear'];
    }
    if (queryParams['toYear']) {
      filters.toYear = queryParams['toYear'];
    }

    // Parse price range
    if (queryParams['minPrice']) {
      const minPrice = parseFloat(queryParams['minPrice']);
      if (!isNaN(minPrice)) {
        filters.minPrice = minPrice;
      }
    }
    if (queryParams['maxPrice']) {
      const maxPrice = parseFloat(queryParams['maxPrice']);
      if (!isNaN(maxPrice)) {
        filters.maxPrice = maxPrice;
      }
    }

    // Parse sort option
    if (queryParams['sortBy']) {
      const sortBy = parseInt(queryParams['sortBy'], 10);
      if (!isNaN(sortBy)) {
        filters.sortBy = sortBy;
      }
    }

    // Parse page
    if (queryParams['page']) {
      const page = parseInt(queryParams['page'], 10);
      if (!isNaN(page) && page > 0) {
        filters.page = page;
      }
    }

    return filters;
  }

  /**
   * Update URL with current filter state
   */
  updateUrlWithFilters(filterState: FilterState, replaceUrl: boolean = false): void {
    const queryParams: any = {};

    // Add category
    if (filterState.selectedCategory) {
      queryParams.category = filterState.selectedCategory;
    }

    // Add unit types
    if (filterState.selectedUnitTypes && filterState.selectedUnitTypes.length > 0) {
      queryParams.unitTypes = filterState.selectedUnitTypes.map(ut => ut.id).join(',');
    }

    // Add makes
    if (filterState.selectedMakes && filterState.selectedMakes.length > 0) {
      queryParams.makes = filterState.selectedMakes.map(m => m.id).join(',');
    }

    // Add models
    if (filterState.selectedModels && filterState.selectedModels.length > 0) {
      queryParams.models = filterState.selectedModels.map(m => m.id).join(',');
    }

    // Add year range
    if (filterState.selectedFromYear) {
      queryParams.fromYear = filterState.selectedFromYear;
    }
    if (filterState.selectedToYear) {
      queryParams.toYear = filterState.selectedToYear;
    }

    // Add price range
    if (filterState.minPrice && filterState.minPrice > 0) {
      queryParams.minPrice = filterState.minPrice;
    }
    if (filterState.maxPrice) {
      queryParams.maxPrice = filterState.maxPrice;
    }

    // Add sort option
    if (filterState.selectedSortBy) {
      queryParams.sortBy = filterState.selectedSortBy;
    }

    // Add page (only if not page 1)
    if (filterState.currentPage && filterState.currentPage > 1) {
      queryParams.page = filterState.currentPage;
    }

    // Navigate with new query parameters
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: queryParams,
      queryParamsHandling: 'replace',
      replaceUrl: replaceUrl
    });
  }

  /**
   * Clear all filters from URL
   */
  clearFiltersFromUrl(): void {
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: {},
      queryParamsHandling: 'replace'
    });
  }

  /**
   * Convert URL filters to component filter state
   */
  convertUrlFiltersToState(
    urlFilters: UrlFilterParams,
    availableCategories: FilterCategories[],
    availableUnitTypes: FilterUnitTypes[],
    availableMakes: FilterMakes[],
    availableModels: FilterModels[]
  ): Partial<FilterState> {
    const state: Partial<FilterState> = {};

    // Convert category
    if (urlFilters.category) {
      const category = availableCategories.find(c => c.id === urlFilters.category);
      if (category) {
        state.selectedCategory = category.id;
      }
    }

    // Convert unit types
    if (urlFilters.unitTypes) {
      state.selectedUnitTypes = availableUnitTypes.filter(ut => 
        urlFilters.unitTypes!.includes(ut.id)
      );
    }

    // Convert makes
    if (urlFilters.makes) {
      state.selectedMakes = availableMakes.filter(m => 
        urlFilters.makes!.includes(m.id)
      );
    }

    // Convert models
    if (urlFilters.models) {
      state.selectedModels = availableModels.filter(m => 
        urlFilters.models!.includes(m.id)
      );
    }

    // Convert year range
    if (urlFilters.fromYear) {
      state.selectedFromYear = urlFilters.fromYear;
    }
    if (urlFilters.toYear) {
      state.selectedToYear = urlFilters.toYear;
    }

    // Convert price range
    if (urlFilters.minPrice !== undefined) {
      state.minPrice = urlFilters.minPrice;
    }
    if (urlFilters.maxPrice !== undefined) {
      state.maxPrice = urlFilters.maxPrice;
    }

    // Convert sort option
    if (urlFilters.sortBy) {
      state.selectedSortBy = urlFilters.sortBy;
    }

    // Convert page
    if (urlFilters.page) {
      state.currentPage = urlFilters.page;
    }

    return state;
  }

  /**
   * Generate shareable URL with current filters
   */
  generateShareableUrl(filterState: FilterState): string {
    const baseUrl = window.location.origin + window.location.pathname;
    const queryParams = new URLSearchParams();

    if (filterState.selectedCategory) {
      queryParams.set('category', filterState.selectedCategory.toString());
    }
    if (filterState.selectedUnitTypes && filterState.selectedUnitTypes.length > 0) {
      queryParams.set('unitTypes', filterState.selectedUnitTypes.map(ut => ut.id).join(','));
    }
    if (filterState.selectedMakes && filterState.selectedMakes.length > 0) {
      queryParams.set('makes', filterState.selectedMakes.map(m => m.id).join(','));
    }
    if (filterState.selectedModels && filterState.selectedModels.length > 0) {
      queryParams.set('models', filterState.selectedModels.map(m => m.id).join(','));
    }
    if (filterState.selectedFromYear) {
      queryParams.set('fromYear', filterState.selectedFromYear);
    }
    if (filterState.selectedToYear) {
      queryParams.set('toYear', filterState.selectedToYear);
    }
    if (filterState.minPrice && filterState.minPrice > 0) {
      queryParams.set('minPrice', filterState.minPrice.toString());
    }
    if (filterState.maxPrice) {
      queryParams.set('maxPrice', filterState.maxPrice.toString());
    }
    if (filterState.selectedSortBy) {
      queryParams.set('sortBy', filterState.selectedSortBy.toString());
    }

    const queryString = queryParams.toString();
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  }
}
