import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { API_URL_UTIL, BaseComponent } from '@core/utils';
import { DealerService } from '@pages/administration/pages/dealers/dealers.service';
import { AuthService } from '@pages/auth/services/auth.service';
import { QuotationValues } from '@pages/crm/models/customer-lead-quotation.model';
import { DealerDetails, FilterCategories, FilterMakes, FilterModels, FilterUnitTypes, OrderParams, PublicInventory, SortingOptions } from '@pages/public-inventories/models';
import { InventoriesService } from '@pages/public-inventories/services';
import { UrlFilterService } from '@pages/public-inventories/services/url-filter.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Observable, Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { DataType, IdNameModel, OperatorType, Page, TreeOperatorType } from 'src/app/@shared/models';
import { THEMES, ThemeService } from 'src/app/@shared/services/theme.service';

@Component({
  selector: 'app-inventory-list',
  templateUrl: './inventory-list.component.html',
  styleUrls: ['./inventory-list.component.scss'],
})
export class InventoryListComponent extends BaseComponent implements OnInit, OnDestroy {
  NO_BANNER = 1;
  inventories: Array<PublicInventory> = [];
  categories!: Array<FilterCategories>;
  isLoggedIn$!: Observable<boolean>;
  dealerName!: string;
  dealerDetails!: DealerDetails;
  dealerOptions: IdNameModel[] = [];
  dealerOptionsLoader = false;
  noDealerExist = false;
  openModal = false;
  unitTypes!: Array<FilterUnitTypes>;
  makes!: Array<FilterMakes>;
  models!: Array<FilterModels>;
  selectedCategory!: number | null;
  selectedUnitTypes!: Array<FilterUnitTypes>;
  selectedMakes!: Array<FilterMakes>;
  selectedModels!: Array<FilterModels>;
  selectedFromYear!: string | null;
  selectedToYear!: string | null;
  minPrice: any = 0;
  maxPrice: number | null = null;
  selectedSortBy!: number | null;
  searchUnitType!: string;
  searchMake!: string;
  searchModel!: string;
  headerSearch!: string;
  showAllUnits = false;
  showAllMakes = false;
  showAllModel = false;
  minPriceSearch = new Subject<void>();
  maxPriceSearch = new Subject<void>();
  selectedDealer!: string;
  filterParams = {
    treeOperator: TreeOperatorType.AND,
    right: {
      treeOperator: TreeOperatorType.OR,
      values: [] as Array<QuotationValues>
    },
    left: {
      treeOperator: TreeOperatorType.NOOP,
      values: [] as Array<QuotationValues>
    }
  }

  filterKeys = {
    category: 'generalInformation.unitTypeCategory.id',
    date: 'generalInformation.year',
    price: 'financial.retailAskingPrice',
    unitType: 'generalInformation.unitType.id',
    make: 'generalInformation.make.id',
    model: 'generalInformation.unitModel.id',
    modelName: 'generalInformation.unitModel.name',
    makeName: 'generalInformation.make.name',
    miles: 'odometer.odometerReading',
    createdDate: 'createdDate',
    stockNumber: 'generalInformation.stockNumber'
  }
  isDarkMode = false;
  sortByOptions = [
    {
      id: 1,
      label: 'Price (low to high)',
      ascending: true,
      field: this.filterKeys.price
    },
    {
      id: 2,
      label: 'Price (high to low)',
      ascending: false,
      field: this.filterKeys.price
    },
    {
      id: 3,
      label: 'Miles (low to high)',
      ascending: true,
      field: this.filterKeys.miles
    },
    {
      id: 4,
      label: 'Miles (high to low)',
      ascending: false,
      field: this.filterKeys.miles
    },
    {
      id: 5,
      label: 'Just Arrived',
      ascending: false,
      field: this.filterKeys.createdDate
    }
  ];

  orderByOptions!: OrderParams | null;
  minValue = 0;
  maxValue = this.constants.maxPriceAmount;
  isMaxValue = false;
  prevMinValue: number = this.minValue;
  prevMaxValue: number = this.maxValue;
  // Initialize sliderValue as a two-element array to store the min and max slider values
  sliderValue: [number, number];
  sidebarVisible = false;

  // Flag to prevent URL updates during filter application from URL
  isApplyingUrlFilters = false;

  // Global loading state for full-screen loader
  isGlobalLoading = false;

  // Flag to prevent multiple simultaneous API calls
  isLoadingInventories = false;

  // Flag to track if filter options are loaded
  filterOptionsLoaded = false;

  // Timeout for debouncing URL updates
  urlUpdateTimeout: any;
  constructor(
    private readonly inventoriesService: InventoriesService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdf: ChangeDetectorRef,
    private readonly themeService: ThemeService,
    private readonly dealerService: DealerService,
    private readonly urlFilterService: UrlFilterService,
  ) {
    super();
    this.paginationConfig.itemsPerPageOptions = [12, 24, 48, 100];
    this.paginationConfig.itemsPerPage = 12;
  }

  ngOnInit(): void {
    this.isLoggedIn$ = this.authService.isLoggedIn$;
    this.subscribeToTheme();
    this.getDealerName();
    this.getDealersList();
    this.minPriceFilter();
    this.maxPriceFilter();
    this.activatedRoute.queryParams
      .subscribe(params => {
        if (params?.id) {
          this.openModal = true;
        }
      })
    window.addEventListener('popstate', this.closeModal);
    this.sliderValue = [this.minPrice, this.maxPrice];
  }

  getFilterOptions(): void {
    this.filterOptionsLoaded = false;

    // Load all filter options
    this.getCategories();
    this.getUnitType();
    this.getMakes();
    this.getModels();

    // Mark filter options as loaded after a short delay
    setTimeout(() => {
      this.filterOptionsLoaded = true;

      // Apply URL filters only after filter options are loaded
      if (!this.isApplyingUrlFilters) {
        console.log('Applying URL filters after options are loaded');
        this.initializeFiltersFromUrl();
      }
    }, 1000);
  }

  /**
   * Initialize filters from URL parameters
   */
  private initializeFiltersFromUrl(): void {
    // Only apply URL filters if filter options are loaded
    if (!this.filterOptionsLoaded) {
      return;
    }

    const urlFilters = this.urlFilterService.getFiltersFromUrl();

    if (Object.keys(urlFilters).length === 0) {
      // No URL filters to apply, just load inventory
      this.getInventories(this.dealerName);
      return;
    }

    // Apply URL filters
    this.applyUrlFiltersToComponent(urlFilters);
  }

  getDealerName(): void {
    this.activatedRoute.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.dealerName = params.dealerName;
      this.selectedDealer = params.dealerName;
      // this.getInventories(params.dealerName);
      // console.log("getDealerName");
      this.getDealerDetails(params.dealerName);
    });
  }

  getDealerDetails(dealerName?: string): void {
    this.inventoriesService.get<DealerDetails>(`${API_URL_UTIL.subDomain.dealer}/${API_URL_UTIL.subDomain.details}/${API_URL_UTIL.subDomain.abbreviation}/${dealerName}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: DealerDetails) => {
          this.dealerDetails = res;

          // Get filter options - URL filters will be applied after options are loaded
          // (see getFilterOptions method)
          this.getFilterOptions();
        }
      });
  }

  getDealersList(): void {
    this.dealerOptionsLoader = true;
    this.dealerService.getDealerList<IdNameModel>().pipe(takeUntil(this.destroy$)).subscribe({
      next: (dealerOptions) => {
        this.dealerOptions = dealerOptions;
        this.dealerOptionsLoader = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.dealerOptionsLoader = false;
        this.cdf.detectChanges();
      }
    });
  }

  changeDealer(): void {
    this.router.navigate(['public-inventory/' + this.selectedDealer]);
  }

  onPageChanged(_event: PageChangedEvent): void {
    this.getInventories();
    this.updateUrlWithCurrentFilters();
  }

  getInventories(dealerName?: string): void {
    // Prevent multiple simultaneous calls
    if (this.isLoadingInventories) {
      return;
    }

    // Use only global loading to prevent multiple loaders
    this.isLoadingInventories = true;
    this.isGlobalLoading = true;
    this.isLoading = false; // Disable the local loader

    this.inventoriesService.getListWithFiltersWithPagination<{}, PublicInventory>(this.getInventoriesParams(dealerName), this.paginationConfig.page, this.paginationConfig.itemsPerPage, `${API_URL_UTIL.inventory.root}/${API_URL_UTIL.inventory.list}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Page<PublicInventory>) => {
          this.inventories = res.content;
          this.setPaginationParamsFromPageResponse<PublicInventory>(res);
          this.isGlobalLoading = false;
          this.isLoadingInventories = false;
          this.noDealerExist = false;
        },
        error: () => {
          this.isGlobalLoading = false;
          this.isLoadingInventories = false;
          this.noDealerExist = true;
        }
      });
  }

  getInventoriesParams(dealerName?: string): any {
    const params = { "abbreviation": dealerName ?? this.dealerName, orderBy: this.orderByOptions ? [this.orderByOptions] : [] }
    if (this.filterParams?.left?.values) {
      this.filterParams.left.values = this.filterParams.left.values.filter(value => value.value && value?.value?.toString());
    }
    if (this.filterParams.left?.values?.length) {
      if (this.filterParams.left.values.length > 1) {
        this.filterParams.left.treeOperator = TreeOperatorType.AND;
      } else {
        this.filterParams.left.treeOperator = TreeOperatorType.NOOP;
      }
    } else {
      this.filterParams.left = {} as any;
    }

    if (!this.filterParams?.right?.values?.length) {
      this.filterParams.right = {} as any;
    }

    return { ...params, ...this.filterParams };
  }

  getCategories(): void {
    this.inventoriesService.get<Array<FilterCategories>>(`${API_URL_UTIL.inventory.all}/${API_URL_UTIL.inventory.category}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<FilterCategories>) => {
          this.categories = res;
        }
      });
  }

  getUnitType(categoryId?: number): void {
    this.inventoriesService.get<Array<FilterUnitTypes>>(`${API_URL_UTIL.inventory.unitTypes}?categoryId=${categoryId ?? ''}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<FilterUnitTypes>) => {
          this.unitTypes = res;
          this.cdf.detectChanges();
        }
      });
  }

  getMakes(categoryId?: number): void {
    this.inventoriesService.get<Array<FilterMakes>>(`${API_URL_UTIL.inventory.makes}?categoryId=${categoryId ?? ''}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<FilterMakes>) => {
          this.makes = res;
          this.cdf.detectChanges();
        }
      })
  }

  getModels(makeIds?: Array<number>): void {
    this.inventoriesService.add<Array<FilterModels>>((makeIds?.length ? makeIds : undefined) as any, `${API_URL_UTIL.inventory.unitModel}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: Array<FilterModels>) => {
          this.models = res;
          this.cdf.detectChanges();
        }
      });
  }

  onCategoryChange(categoryId?: number): void {
    // Show global loader
    this.isGlobalLoading = true;

    this.changeFilterParams(
      DataType.LONG,
      OperatorType.EQUAL,
      this.filterKeys.category,
      null,
      categoryId
    );
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return value.key !== this.filterKeys.unitType && value.key !== this.filterKeys.make && value.key !== this.filterKeys.model
    })
    this.selectedUnitTypes = this.selectedMakes = this.selectedModels = [];
    this.getUnitType(categoryId);
    this.getMakes(categoryId);
    this.getModels();
    this.updateUrlWithCurrentFilters();
  }

  async onUnitTypeChange(): Promise<void> {
    const unitTypeIds: Array<number> = this.selectedUnitTypes.map(({ id }: any) => id);
    this.changeFilterParams(
      DataType.LONG,
      OperatorType.IN,
      this.filterKeys.unitType,
      null,
      unitTypeIds
    );
    this.updateUrlWithCurrentFilters();
  }

  async onMakeChange(): Promise<void> {
    const makeIds: Array<number> = this.selectedMakes.map(({ id }: any) => id);
    this.changeFilterParams(
      DataType.LONG,
      OperatorType.IN,
      this.filterKeys.make,
      null,
      makeIds
    );
    this.getModels(makeIds);
    this.updateUrlWithCurrentFilters();
  }

  async onModelChange(): Promise<void> {
    const modelIds: Array<number> = this.selectedModels.map(({ id }: any) => id);
    this.changeFilterParams(
      DataType.LONG,
      OperatorType.IN,
      this.filterKeys.model,
      null,
      modelIds
    );
    this.updateUrlWithCurrentFilters();
  }

  onStartDateChange(): void {
    const startDateFilter = this.filterParams?.left?.values?.find(
      value => value.key === this.filterKeys.date && value.operator === OperatorType.GREATER_THAN_OR_EQUAL
    );
    this.changeFilterParams(
      DataType.INTEGER,
      OperatorType.GREATER_THAN_OR_EQUAL,
      this.filterKeys.date,
      startDateFilter,
      this.selectedFromYear
    );
  }

  onEndDateChange(): void {
    const endDateFilter = this.filterParams?.left?.values?.find(
      value => value.key === this.filterKeys.date && value.operator === OperatorType.LESS_THAN_OR_EQUAL
    );
    this.changeFilterParams(
      DataType.INTEGER,
      OperatorType.LESS_THAN_OR_EQUAL,
      this.filterKeys.date,
      endDateFilter,
      this.selectedToYear
    );
  }

  minPriceFilter(): void {
    this.minPriceSearch.pipe(debounceTime(1000), takeUntil(this.destroy$)).subscribe(() => {
      const minPriceFilter = this.filterParams?.left?.values?.find(
        value => value.key === this.filterKeys.price && value.operator === OperatorType.GREATER_THAN_OR_EQUAL
      );
      if (this.minPrice) {
        this.changeFilterParams(
          DataType.DOUBLE,
          OperatorType.GREATER_THAN_OR_EQUAL,
          this.filterKeys.price,
          minPriceFilter,
          this.minPrice
        );
      } else {
        this.filterParams.left.values = this.filterParams?.left?.values?.filter(value => {
          return !(value.key === this.filterKeys.price && value.operator === OperatorType.GREATER_THAN_OR_EQUAL);
        });
        this.getInventories(this.dealerName);
      }
    });
  }

  maxPriceFilter(): void {
    this.maxPriceSearch.pipe(debounceTime(1000), takeUntil(this.destroy$)).subscribe(() => {
      const maxPriceFilter = this.filterParams?.left?.values?.find(
        value => value.key === this.filterKeys.price && value.operator === OperatorType.LESS_THAN_OR_EQUAL
      );
      if (this.maxPrice) {
        this.changeFilterParams(
          DataType.DOUBLE,
          OperatorType.LESS_THAN_OR_EQUAL,
          this.filterKeys.price,
          maxPriceFilter,
          this.maxPrice
        );
      } else {
        this.filterParams.left.values = this.filterParams?.left?.values?.filter(value => {
          return !(value.key === this.filterKeys.price && value.operator === OperatorType.LESS_THAN_OR_EQUAL);
        });
        this.getInventories(this.dealerName);
      }
    });

  }

  onMinPriceChange(): void {
    // Ensure minValue is not greater than maxValue
    if (this.minPrice > this.maxValue) {
      this.isMaxValue = true;
      this.minPrice = 0;
      this.sliderValue = [this.minPrice, this.maxPrice];
    } else {
      if (this.minPrice > this.maxPrice) {
        this.maxPrice = this.minPrice;
      }
      this.isMaxValue = false;
      this.sliderValue = [this.minPrice, this.maxPrice]; // Update the slider range
      this.minPriceSearch.next();
    }
    this.updateUrlWithCurrentFilters();
  }

  onMaxPriceChange(): void {
    // Ensure maxValue is not less than minValue
    if (this.maxPrice > this.maxValue) {
      this.isMaxValue = true;
      this.maxPrice = this.constants.maxPriceAmount;
      this.sliderValue = [this.minPrice, this.maxPrice];
    } else {
      if (this.maxPrice < this.minPrice) {
        this.minPrice = this.maxPrice;
      }
      this.isMaxValue = false;
      this.sliderValue = [this.minPrice, this.maxPrice]; // Update the slider range
      this.maxPriceSearch.next();
    }
    this.updateUrlWithCurrentFilters();
  }

  changeFilterParams(dataType: string, operator: string, key: string, defaultFilter?: any, value?: any): void {
    if (!this.filterParams.left?.values?.length) {
      this.filterParams.left.treeOperator = TreeOperatorType.NOOP;
      this.filterParams.left.values = [];
    }
    const selectedFilter = key === this.filterKeys.date || key === this.filterKeys.price
      ? defaultFilter
      : this.filterParams.left?.values.find(value => value.key === key);
    if (selectedFilter) {
      selectedFilter.value = value;
    } else {
      this.filterParams.left.values.push({
        dataType: dataType,
        key: key,
        operator: operator,
        value: value
      })
    }
    this.paginationConfig.page = 1;
    setTimeout(() => {
      this.getInventories(this.dealerName);
    }, 1000)
  }

  clearAll(): void {
    this.filterParams.left.values = [];
    this.selectedCategory = this.maxPrice = this.selectedToYear = this.selectedFromYear = this.minPrice = null;
    this.selectedUnitTypes = this.selectedMakes = this.selectedModels = [];
    this.orderByOptions = null;
    this.selectedSortBy = null;
    this.paginationConfig.page = 1;
    this.minPrice = 0;
    this.maxPrice = null;
    this.sliderValue = [this.minPrice, this.maxPrice];
    this.getFilterOptions();
    this.getInventories(this.dealerName);
    this.sidebarVisible = false;
    this.urlFilterService.clearFiltersFromUrl();
  }

  redirectToDetailPage(id: number): void {
    this.router.navigateByUrl(`./${{ id }}`);
  }

  trackByFunction(index: number, element: any) {
    return element ? index : null;
  }

  onHeaderSearch(): void {
    if (this.headerSearch.length) {
      this.filterParams.right = {
        treeOperator: TreeOperatorType.NOOP,
        values: [
          {
            dataType: DataType.STRING,
            key: this.filterKeys.stockNumber,
            operator: OperatorType.LIKE,
            value: this.headerSearch
          }
        ]
      };
      this.paginationConfig.page = 1;
    } else {
      this.filterParams.right = {} as any;
    }
    this.getInventories(this.dealerName);
  }

  sortBy(selectedSortByOption: SortingOptions): void {
    if (!selectedSortByOption?.value) {
      this.orderByOptions = null;
      this.getInventories(this.dealerName);
      this.updateUrlWithCurrentFilters();
      return;
    }
    const selectedOption = this.sortByOptions.find(option => option.id === selectedSortByOption.value);
    this.orderByOptions = { ascending: selectedOption?.ascending, field: selectedOption?.field };
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  getSelectedCategoryName(selectedCategoryId: number): string {
    return `Category: ${this.categories.find(category => category.id === selectedCategoryId)?.name}`;
  }

  getSelectedUnitTypeName(): string {
    const length = ` +${this.selectedUnitTypes?.length - 1}`;
    return `Unit type: ${this.selectedUnitTypes[0]?.name}${(this.selectedUnitTypes?.length > 1 ? length : '')}`;
  }

  getSelectedMakeName(): string {
    const length = ` +${this.selectedMakes?.length - 1}`;
    return `Make: ${this.selectedMakes[0]?.name}${(this.selectedMakes?.length > 1 ? length : '')}`
  }

  getSelectedModelName(): string {
    const length = ` +${this.selectedModels?.length - 1}`;
    return `Model: ${this.selectedModels[0]?.name}${(this.selectedModels?.length > 1 ? length : '')}`
  }

  clearCategoryFilter(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return value.key !== this.filterKeys.unitType &&
        value.key !== this.filterKeys.make &&
        value.key !== this.filterKeys.model &&
        value.key !== this.filterKeys.category;
    });
    this.selectedCategory = null;
    this.selectedUnitTypes = this.selectedMakes = this.selectedModels = [];
    this.getUnitType();
    this.getMakes();
    this.getModels();
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearUnitTypes(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return value.key !== this.filterKeys.unitType;
    });
    this.selectedUnitTypes = [];
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearMakes(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return value.key !== this.filterKeys.make && value.key !== this.filterKeys.model;
    });
    this.selectedMakes = this.selectedModels = [];
    this.getModels();
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearModels(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return value.key !== this.filterKeys.model;
    });
    this.selectedModels = [];
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearFromYear(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return !(value.key === this.filterKeys.date && value.operator === OperatorType.GREATER_THAN_OR_EQUAL);
    });
    this.selectedFromYear = null;
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearToYear(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return !(value.key === this.filterKeys.date && value.operator === OperatorType.LESS_THAN_OR_EQUAL);
    });
    this.selectedToYear = null;
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearMinPrice(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return !(value.key === this.filterKeys.price && value.operator === OperatorType.GREATER_THAN_OR_EQUAL);
    });
    this.minPrice = 0;
    this.sliderValue = [this.minPrice, this.maxPrice];
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  clearMaxPrice(): void {
    this.filterParams.left.values = this.filterParams.left.values.filter((value: any) => {
      return !(value.key === this.filterKeys.price && value.operator === OperatorType.LESS_THAN_OR_EQUAL);
    });
    this.maxPrice = null;
    this.sliderValue = [this.minPrice, this.maxPrice];
    this.getInventories(this.dealerName);
    this.updateUrlWithCurrentFilters();
  }

  subscribeToTheme(): void {
    this.themeService.selectedTheme$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((theme: THEMES) => {
        if (theme === THEMES.DARK) {
          this.isDarkMode = true;
        } else {
          this.isDarkMode = false;
        }
        this.cdf.detectChanges();
      });
  }

  goToDetailPage(id: number): void {
    this.openModal = true;
    this.redirectTo(`${this.path.publicInvetory.root}/${this.dealerName}?id=${id}&abbreviation=${this.dealerName}`);
  }

  closeModal = (): void => {
    this.openModal = false;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    document.body.style.overflow = 'auto';
    window.removeEventListener('popstate', this.closeModal);

    // Clean up timeouts
    if (this.urlUpdateTimeout) {
      clearTimeout(this.urlUpdateTimeout);
    }
  }

  redirectTo(url: string): void {
    this.router.navigateByUrl(url);
  }

  // Handle slider change
  onSliderChange(value: any): void {
    if (value) {
      this.minPrice = this.sliderValue[0]
      this.maxPrice = this.sliderValue[1]
      // Check if min or max value has changed
      if (this.minPrice !== this.prevMinValue) {
        this.prevMinValue = this.minPrice; // Update the previous min value
        this.onMinPriceChange();
      }
  
      if (this.maxPrice !== this.prevMaxValue) {
        this.prevMaxValue = this.maxPrice; // Update the previous max value
        this.onMaxPriceChange();
      } 
    }
   
  }

  onApplyFilter(): void {
     setTimeout(() => {
      this.getInventories(this.dealerName);
    }, 1000)
    this.sidebarVisible = false;
    this.updateUrlWithCurrentFilters();
  }



  /**
   * Apply URL filters to component state
   */
  private applyUrlFiltersToComponent(urlFilters: any): void {
    // Set flag to prevent URL updates during filter application
    this.isApplyingUrlFilters = true;

    const filterState = this.urlFilterService.convertUrlFiltersToState(
      urlFilters,
      this.categories || [],
      this.unitTypes || [],
      this.makes || [],
      this.models || []
    );

    // Apply filters directly to component state without triggering URL updates

    // Apply category filter
    if (filterState.selectedCategory) {
      this.selectedCategory = filterState.selectedCategory;
      // Get unit types and makes for this category
      this.getUnitType(this.selectedCategory);
      this.getMakes(this.selectedCategory);

      // Add category filter
      this.changeFilterParams(
        DataType.LONG,
        OperatorType.EQUAL,
        this.filterKeys.category,
        null,
        this.selectedCategory
      );
    }

    // Apply unit types filter
    if (filterState.selectedUnitTypes && filterState.selectedUnitTypes.length > 0) {
      this.selectedUnitTypes = filterState.selectedUnitTypes;
      const unitTypeIds = this.selectedUnitTypes.map(({ id }: any) => id);
      this.changeFilterParams(
        DataType.LONG,
        OperatorType.IN,
        this.filterKeys.unitType,
        null,
        unitTypeIds
      );
    }

    // Apply makes filter
    if (filterState.selectedMakes && filterState.selectedMakes.length > 0) {
      this.selectedMakes = filterState.selectedMakes;
      const makeIds = this.selectedMakes.map(({ id }: any) => id);
      this.changeFilterParams(
        DataType.LONG,
        OperatorType.IN,
        this.filterKeys.make,
        null,
        makeIds
      );

      // Get models for selected makes
      this.getModels(makeIds);
    }

    // Apply models filter
    if (filterState.selectedModels && filterState.selectedModels.length > 0) {
      this.selectedModels = filterState.selectedModels;
      const modelIds = this.selectedModels.map(({ id }: any) => id);
      this.changeFilterParams(
        DataType.LONG,
        OperatorType.IN,
        this.filterKeys.model,
        null,
        modelIds
      );
    }

    // Apply year filters
    if (filterState.selectedFromYear) {
      this.selectedFromYear = filterState.selectedFromYear;
      this.changeFilterParams(DataType.STRING, OperatorType.GREATER_THAN_OR_EQUAL, this.filterKeys.date, null, this.selectedFromYear);
    }
    if (filterState.selectedToYear) {
      this.selectedToYear = filterState.selectedToYear;
      this.changeFilterParams(DataType.STRING, OperatorType.LESS_THAN_OR_EQUAL, this.filterKeys.date, null, this.selectedToYear);
    }

    // Apply price filters
    if (filterState.minPrice !== undefined) {
      this.minPrice = filterState.minPrice;
      this.changeFilterParams(DataType.LONG, OperatorType.GREATER_THAN_OR_EQUAL, this.filterKeys.price, null, this.minPrice);
    }
    if (filterState.maxPrice !== undefined) {
      this.maxPrice = filterState.maxPrice;
      this.changeFilterParams(DataType.LONG, OperatorType.LESS_THAN_OR_EQUAL, this.filterKeys.price, null, this.maxPrice);
    }

    // Apply sort filter
    if (filterState.selectedSortBy) {
      this.selectedSortBy = filterState.selectedSortBy;
      const selectedOption = this.sortByOptions.find(option => option.id === filterState.selectedSortBy);
      if (selectedOption) {
        this.orderByOptions = { ascending: selectedOption.ascending, field: selectedOption.field };
      }
    }

    // Apply page filter
    if (filterState.currentPage) {
      this.paginationConfig.page = filterState.currentPage;
    }

    // Update slider value
    this.sliderValue = [this.minPrice, this.maxPrice];

    // Trigger inventory fetch with applied filters (only once)
    setTimeout(() => {
      this.getInventories(this.dealerName);

      // Reset flag after filter application
      this.isApplyingUrlFilters = false;
    }, 100);
  }

  /**
   * Update URL with current filter state
   */
  private updateUrlWithCurrentFilters(): void {
    // Skip URL updates when applying filters from URL to prevent loops
    if (this.isApplyingUrlFilters || !this.filterOptionsLoaded) {
      return;
    }

    // Debounce URL updates to prevent multiple rapid changes
    if (this.urlUpdateTimeout) {
      clearTimeout(this.urlUpdateTimeout);
    }

    this.urlUpdateTimeout = setTimeout(() => {
      const filterState: any = {
        selectedCategory: this.selectedCategory,
        selectedUnitTypes: this.selectedUnitTypes || [],
        selectedMakes: this.selectedMakes || [],
        selectedModels: this.selectedModels || [],
        selectedFromYear: this.selectedFromYear,
        selectedToYear: this.selectedToYear,
        minPrice: this.minPrice || 0,
        maxPrice: this.maxPrice,
        selectedSortBy: this.selectedSortBy,
        currentPage: this.paginationConfig.page || 1
      };

      this.urlFilterService.updateUrlWithFilters(filterState, true);
    }, 100);
  }

}
