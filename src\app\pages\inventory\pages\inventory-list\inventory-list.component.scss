@import 'src/assets/scss/variables';

.checkbox-col {
  width: 55px;
}
.small-col {
  width: 100px;
  text-align: center !important;
}

.internet-groups-cell {
  min-width: 120px;
}

.internet-groups-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  overflow: hidden;
  width: 100%;
}

.internet-badge{
  ::ng-deep{
    .p-tag {
      background-color: $badge-background-color !important;
      color: $badge-color !important;
      display: inline-flex !important;
      align-items: center !important;
      text-wrap: nowrap !important;
      white-space: nowrap !important;
      flex-shrink: 0;
    }
  }
}

.dynamic-tag {
  transition: opacity 0.2s ease;
  flex-shrink: 0;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::ng-deep {
    .p-tag {
      white-space: nowrap !important;
      flex-shrink: 0 !important;
    }
  }
}

.show-more-badge {
  flex-shrink: 0;
  white-space: nowrap;
  min-width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #6c757d;
  color: white;
  font-size: 12px;
  cursor: pointer;

  &:hover {
    background-color: #5a6268;
  }
}

@media (max-width: 768px) {
  .internet-groups-cell {
    min-width: 120px;
    max-width: 200px;
  }

  .dynamic-tag {
    max-width: 100px;
  }
}

@media (min-width: 1200px) {
  .internet-groups-cell {
    min-width: 200px;
    max-width: 400px;
  }

  .dynamic-tag {
    max-width: 200px;
  }
}

.show-more-badge {
  background-color: $badge-background-color !important;
  color: $badge-color !important;
  height: 22px;
  width: 22px;
  cursor: pointer;
  margin-right: 0.5rem;
}

::ng-deep .badge-tooltip {
  max-width: 300px !important;
  opacity: 1 !important;

  .p-tooltip-arrow {
    display: none !important;
  }

  .p-tooltip-text {
    display: flex !important;
    flex-direction: column !important;
    flex-wrap: nowrap !important;
    gap: 4px !important;
    padding: 6px !important;
    background-color: white !important;
    color: var(--text-color) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    border-radius: 4px !important;
    border: 1px solid #e0e0e0 !important;
    width: auto !important;

    .tooltip-badge {
      display: inline-block;
      background-color: $badge-background-color;
      color: $badge-color;
      border-radius: 4px;
      padding: 0.15rem 0.4rem;
      font-size: 0.75rem;
      white-space: nowrap;
      margin: 2px;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.associate-col {
  width: 170px;
}

.medium-col {
  width: 250px;
  text-align: center !important;
}

.w-145 {
  width: 145px !important;
}

.inventory-search-tr {
  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield; /* For Firefox */
  }
}

::ng-deep .inventory-page-table .p-dropdown {
  height: 40px;

  .p-dropdown-label {
    display: flex !important;
    align-items: center !important;
  }
}

::ng-deep .inventory-list {
  .normal-dropdowns {
    .p-dropdown {
      height: 40px !important;

      .p-dropdown-label {
        display: flex !important;
        align-items: center !important;
      }
    }
  }
  .p-dropdown .p-dropdown-label {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
    font-size: 14px;
    height: 40px !important;
  }

  .p-dropdown .p-dropdown-clear-icon{
    color: var(--white-color) !important;
  }

  .p-dropdown .p-dropdown-trigger {
    background: var(--active-color) !important;
    color: var(--white-color) !important;
  }

  .p-dropdown {
    background-color: var(--active-color) !important;
    height: inherit;
  }

  .pi-chevron-down:before {
    font-size: 14px;
  }

  .priority-icon {
    padding: 17px !important;
  }
}

::ng-deep .status-data{
  .p-dropdown .p-dropdown-label {
    height: 30px !important;
  }
}

.inventory-list-image {
  // TODO Will be remove once complete
  // max-width: 100%;
  // height: auto;
  // object-fit: contain;
  // display: block;
  // border: 1px solid #7510785c;
  width: 60px;
  height: 40px;
}

.td-inventory-list-image {
  text-align: left !important;
}

.view-inventory {
  text-decoration: underline;
  color: var(--link-color);
  cursor: pointer;
}

.red {
  color: $red-highlight-color;
}

::ng-deep .sold .switch.checked {
  background: $red-highlight-color !important;
}

.incoming-truck {
  height: 20px;
}

.centre {
  margin-left: 24px;
}

.ml-48 {
  margin-left: 48px;
}

.cols-btn {
  margin-right: 5px;
}

.tabs {
  position: relative;
}

.column-btn {
  color: #0b0b69;
  background-color: #fff;
  border-color: #0b0b69;
  font-weight: 600;
  border: 3px solid #0b0b69;
  padding: 0px 17px !important;
  margin-left: 1rem;

  fa-icon {
    margin-left: 5px;
    font-size: 16px;
    margin-right: 9px;
  }
}

.recent-btn {
  padding: 0 1rem !important;
  background-color: var(--active-color);
  border: none;
}

::ng-deep .inventory-search-tr th {
  .search-input {
    input {
      min-width: 4rem !important;
      height: 36px !important;
    }

    p-calendar.p-inputwrapper {
      span.p-calendar-w-btn {
        height: 36px !important;
        min-width: 170px !important;
      }
    }

    .p-multiselect {
      width: 100%;
    }
  }

  .float-end img {
    cursor: pointer;
  }

  .reset-icon {
    fa-icon {
      color: var(--active-color) !important;
      font-size: 23px;
    }
  }
}

.clear-btn-search {
  display: flex;
  align-items: center;
  border: 1px solid var(--border-color-dark) !important;
  border-radius: 9px !important;
  background-color: white;
  padding-left: 5px;

  .form-control {
    border: none !important;
    box-shadow: none !important;
  }
}

.actions {
  min-width: 8rem !important;
  text-align: center !important;

  .save-preference-icon {
    font-size: 22px;
    color: var(--active-color);
  }
}

.actions-content {
  img {
    margin: 0;
  }
  .col-3 {
    display: flex;
    justify-content: center;
  }
}

.btn.inventory-reset-btn {
  padding: 0 15px;
}

.cross-icon {
  position: relative;
  left: 150px;
  top: -28px;
  height: 0;
  display: flex;
  color: #6c757d;
  font-size: 15px;
}

@media only screen and (min-width: 860px) {
  .recent-btn {
    .img-span {
      display: none;
    }
  }
}
@media only screen and (max-width: 860px) {
  .column-btn {
    display: none;
  }

  .recent-btn {
    padding: 0 10px !important;
    .text-span {
      display: none;
    }
  }
}
.image-container {
  width: 100%;
  max-width: 300px; /* or any desired size */
  height: auto;
  overflow: hidden;
}